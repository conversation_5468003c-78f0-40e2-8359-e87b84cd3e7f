import pandas as pd
import numpy as np
import re
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any, Union
from pydantic import BaseModel, Field
import json
import os
from langchain.agents import <PERSON><PERSON>, AgentExecutor, LLMSingleActionAgent, AgentOutputParser
from langchain.prompts import String<PERSON>romptTemplate
from langchain.chains import <PERSON><PERSON>hain
from langchain_openai import ChatOpenAI
from langchain.tools import BaseTool
from langchain.schema import AgentAction, AgentFinish
from langchain.memory import ConversationBufferMemory
from langchain.callbacks.manager import CallbackManagerForToolRun
from langchain.callbacks.base import BaseCallbackHandler
from trading_agent import MarketData, TrailingStopConfig, StrategyParameters, TradingAgent, TradeSignal
from strategy_trainer_agent import StrategyTrainerAgent

class TradePosition(BaseModel):
    """Model for tracking an open trade position"""
    symbol: str
    entry_price: float
    entry_time: datetime
    position_size: float
    trailing_stop: float
    take_profit: Optional[float] = None
    initial_stop: float
    current_price: float = 0
    current_pnl: float = 0
    current_pnl_pct: float = 0
    max_price_reached: float = 0
    max_pnl: float = 0
    max_pnl_pct: float = 0
    duration: timedelta = timedelta(0)
    status: str = "open"  # open, closed
    exit_price: Optional[float] = None
    exit_time: Optional[datetime] = None
    exit_reason: Optional[str] = None
    final_pnl: Optional[float] = None
    final_pnl_pct: Optional[float] = None

class LiveTraderAgent:
    """Agent that executes trades using optimized strategies in real-time."""
    
    def __init__(self, openai_api_key=None, max_position_size=100, max_daily_loss_pct=3):
        self.model = "gpt-4o-mini"
        self.has_api_key = openai_api_key is not None
        self.openai_api_key = openai_api_key
        self.trading_agent = None
        self.open_positions = []
        self.closed_positions = []
        self.daily_pnl = 0
        self.max_position_size = max_position_size
        self.max_daily_loss_pct = max_daily_loss_pct
        self.trading_enabled = False
        self.last_update_time = None
        self.trading_log = []
        
        # Initialize LangChain components if API key is available
        if self.has_api_key:
            self.llm = ChatOpenAI(
                model_name=self.model,
                temperature=0.2,
                openai_api_key=self.openai_api_key
            )
            self.setup_langchain_agent()
    
    def setup_langchain_agent(self):
        """Set up the LangChain agent with tools and prompt template"""
        # Define tools for the agent
        tools = [
            Tool(
                name="ExecuteTrade",
                func=self._execute_trade,
                description="Executes a trade based on a signal"
            ),
            Tool(
                name="UpdatePosition",
                func=self._update_position,
                description="Updates an open position with current market data"
            ),
            Tool(
                name="ClosePosition",
                func=self._close_position,
                description="Closes an open position"
            ),
            Tool(
                name="AnalyzeMarket",
                func=self._analyze_market,
                description="Analyzes current market conditions"
            ),
            Tool(
                name="CheckRiskLimits",
                func=self._check_risk_limits,
                description="Checks if risk limits have been exceeded"
            )
        ]
        
        # Define prompt template
        prompt = LiveTraderPromptTemplate(
            template="""You are a Live Trader Agent that executes cryptocurrency trades based on optimized strategies.
            Your goal is to execute trades with precision, manage risk, and maximize profitability.
            
            Current market data:
            {market_data}
            
            Open positions:
            {open_positions}
            
            Trading signals:
            {trading_signals}
            
            Risk parameters:
            - Max position size: {max_position_size} USDT
            - Max daily loss: {max_daily_loss_pct}%
            - Current daily P&L: {daily_pnl} USDT
            
            Your task is to evaluate trading signals, execute trades, and manage open positions.
            
            Available tools:
            {tools}
            
            Use the tools to execute and manage trades. Think step by step about what information you need and how to manage risk effectively.
            
            {agent_scratchpad}
            """,
            input_variables=["market_data", "open_positions", "trading_signals", "max_position_size", "max_daily_loss_pct", "daily_pnl", "tools", "agent_scratchpad"]
        )
        
        # Set up memory
        memory = ConversationBufferMemory(memory_key="chat_history")
        
        # Create LLM chain
        llm_chain = LLMChain(llm=self.llm, prompt=prompt)
        
        # Create agent
        agent = LLMSingleActionAgent(
            llm_chain=llm_chain,
            output_parser=LiveTraderOutputParser(),
            stop=["\nObservation:"],
            allowed_tools=[tool.name for tool in tools]
        )
        
        # Create agent executor
        self.agent_executor = AgentExecutor.from_agent_and_tools(
            agent=agent,
            tools=tools,
            verbose=True,
            memory=memory
        )
    
    def initialize_trading(self, strategy_params: StrategyParameters):
        """Initialize trading with optimized strategy parameters"""
        self.trading_agent = TradingAgent(strategy_params)
        self.trading_enabled = True
        self.last_update_time = datetime.now()
        self.daily_pnl = 0
        self.open_positions = []
        self.trading_log.append({
            "timestamp": self.last_update_time,
            "action": "initialize",
            "message": f"Trading initialized with strategy parameters: {strategy_params.model_dump()}"
        })
    
    def process_market_data(self, market_data: pd.DataFrame, symbol: str, timeframe: str) -> Optional[Dict[str, Any]]:
        """Process new market data and execute trades if signals are generated"""
        if not self.trading_enabled or self.trading_agent is None:
            return None
        
        # Check if we've crossed to a new day and reset daily P&L
        current_time = datetime.now()
        if current_time.date() > self.last_update_time.date():
            self.daily_pnl = 0
            self.trading_log.append({
                "timestamp": current_time,
                "action": "reset_daily_pnl",
                "message": "Daily P&L reset for new trading day"
            })
        
        self.last_update_time = current_time
        
        # Prepare market data for agent
        agent_market_data = self.trading_agent.prepare_market_data(market_data)
        agent_market_data.symbol = symbol
        agent_market_data.timeframe = timeframe
        
        # Update open positions with current market data
        self._update_open_positions(agent_market_data)
        
        # Check risk limits
        risk_status = self._check_risk_limits_internal()
        if risk_status["limits_exceeded"]:
            self.trading_log.append({
                "timestamp": current_time,
                "action": "risk_limit_exceeded",
                "message": f"Risk limits exceeded: {risk_status['reason']}"
            })
            return {
                "action": "risk_limit_exceeded",
                "message": risk_status['reason'],
                "open_positions": [p.model_dump() for p in self.open_positions],
                "daily_pnl": self.daily_pnl
            }
        
        # Get entry signal from trading agent
        signal = self.trading_agent.get_entry_signal(agent_market_data)
        
        # If no API key or not using LangChain, process signal directly
        if not self.has_api_key:
            return self._process_signal_directly(signal, agent_market_data)
        
        # Otherwise, use LangChain agent to process signal
        try:
            # Format market data for agent
            market_data_str = json.dumps({
                "symbol": agent_market_data.symbol,
                "timeframe": agent_market_data.timeframe,
                "current_price": agent_market_data.close_prices[-1],
                "recent_prices": agent_market_data.close_prices[-5:],
                "recent_volumes": agent_market_data.volumes[-5:],
                "timestamp": current_time.isoformat()
            })
            
            # Format open positions for agent
            open_positions_str = json.dumps([p.model_dump() for p in self.open_positions])
            
            # Format trading signals for agent
            trading_signals_str = "No signals" if signal is None else json.dumps({
                "entry_price": signal.entry_price,
                "entry_time": signal.entry_time.isoformat(),
                "direction": signal.direction,
                "confidence": signal.confidence,
                "reason": signal.reason
            })
            
            # Run agent
            result = self.agent_executor.run(
                market_data=market_data_str,
                open_positions=open_positions_str,
                trading_signals=trading_signals_str,
                max_position_size=self.max_position_size,
                max_daily_loss_pct=self.max_daily_loss_pct,
                daily_pnl=self.daily_pnl
            )
            
            # Log agent result
            self.trading_log.append({
                "timestamp": current_time,
                "action": "agent_decision",
                "message": result
            })
            
            return {
                "action": "agent_processed",
                "message": result,
                "open_positions": [p.model_dump() for p in self.open_positions],
                "daily_pnl": self.daily_pnl
            }
            
        except Exception as e:
            self.trading_log.append({
                "timestamp": current_time,
                "action": "error",
                "message": f"Error in agent processing: {str(e)}"
            })
            
            # Fallback to direct processing
            return self._process_signal_directly(signal, agent_market_data)
    
    def _process_signal_directly(self, signal: Optional[TradeSignal], market_data: MarketData) -> Dict[str, Any]:
        """Process trading signal directly without using LangChain agent"""
        current_time = datetime.now()
        current_price = market_data.close_prices[-1]
        
        # Check if we already have an open position for this symbol
        existing_position = next((p for p in self.open_positions if p.symbol == market_data.symbol), None)
        
        if signal and not existing_position:
            # Calculate position size based on risk parameters
            position_size = min(self.max_position_size, self.max_position_size * signal.confidence)
            
            # Calculate initial stop loss
            optimal_stop_distance = self.trading_agent.optimize_trailing_stop(market_data)
            initial_stop = signal.entry_price * (1 - optimal_stop_distance)
            
            # Create new position
            new_position = TradePosition(
                symbol=market_data.symbol,
                entry_price=signal.entry_price,
                entry_time=current_time,
                position_size=position_size,
                trailing_stop=initial_stop,
                initial_stop=initial_stop,
                current_price=current_price,
                max_price_reached=current_price
            )
            
            self.open_positions.append(new_position)
            
            self.trading_log.append({
                "timestamp": current_time,
                "action": "new_position",
                "message": f"Opened new position for {market_data.symbol} at {signal.entry_price} with size {position_size} USDT"
            })
            
            return {
                "action": "position_opened",
                "position": new_position.model_dump(),
                "signal": signal.model_dump() if signal else None,
                "open_positions": [p.model_dump() for p in self.open_positions],
                "daily_pnl": self.daily_pnl
            }
        
        elif existing_position:
            # Update existing position
            self._update_position_internal(existing_position, current_price, current_time, market_data)
            
            # Check if stop loss hit
            if current_price <= existing_position.trailing_stop:
                return self._close_position_internal(existing_position, current_price, current_time, "stop_loss")
            
            # Check if take profit hit (if set)
            if existing_position.take_profit and current_price >= existing_position.take_profit:
                return self._close_position_internal(existing_position, current_price, current_time, "take_profit")
            
            return {
                "action": "position_updated",
                "position": existing_position.model_dump(),
                "open_positions": [p.model_dump() for p in self.open_positions],
                "daily_pnl": self.daily_pnl
            }
        
        return {
            "action": "no_action",
            "message": "No signal generated and no open positions to update",
            "open_positions": [p.model_dump() for p in self.open_positions],
            "daily_pnl": self.daily_pnl
        }
    
    def _update_open_positions(self, market_data: MarketData):
        """Update all open positions with current market data"""
        current_time = datetime.now()
        current_price = market_data.close_prices[-1]
        
        positions_to_remove = []
        
        for position in self.open_positions:
            if position.symbol == market_data.symbol:
                # Update position
                self._update_position_internal(position, current_price, current_time, market_data)
                
                # Check if stop loss hit
                if current_price <= position.trailing_stop:
                    result = self._close_position_internal(position, current_price, current_time, "stop_loss")
                    positions_to_remove.append(position)
                
                # Check if take profit hit (if set)
                elif position.take_profit and current_price >= position.take_profit:
                    result = self._close_position_internal(position, current_price, current_time, "take_profit")
                    positions_to_remove.append(position)
        
        # Remove closed positions from open positions list
        for position in positions_to_remove:
            if position in self.open_positions:
                self.open_positions.remove(position)
    
    def _update_position_internal(self, position: TradePosition, current_price: float, current_time: datetime, market_data: Optional[MarketData] = None):
        """Update a position with current market data"""
        # Update current price and P&L
        position.current_price = current_price
        position.current_pnl = (current_price - position.entry_price) * position.position_size / position.entry_price
        position.current_pnl_pct = (current_price - position.entry_price) / position.entry_price
        position.duration = current_time - position.entry_time
        
        # Update max price and P&L if new highs reached
        if current_price > position.max_price_reached:
            position.max_price_reached = current_price
            position.max_pnl = (current_price - position.entry_price) * position.position_size / position.entry_price
            position.max_pnl_pct = (current_price - position.entry_price) / position.entry_price
            
            # Update trailing stop if using adaptive trailing stop and we have market data
            if market_data and self.trading_agent:
                # Get optimal trailing stop distance
                optimal_stop_distance = self.trading_agent.optimize_trailing_stop(market_data)
                
                # Calculate new potential trailing stop
                potential_new_stop = current_price * (1 - optimal_stop_distance)
                
                # Update trailing stop if new stop is higher than current stop
                if potential_new_stop > position.trailing_stop:
                    position.trailing_stop = potential_new_stop
                    
                    self.trading_log.append({
                        "timestamp": current_time,
                        "action": "update_trailing_stop",
                        "message": f"Updated trailing stop for {position.symbol} to {position.trailing_stop}"
                    })
    
    def _close_position_internal(self, position: TradePosition, exit_price: float, exit_time: datetime, exit_reason: str) -> Dict[str, Any]:
        """Close a position and calculate final P&L"""
        # Update position with exit information
        position.exit_price = exit_price
        position.exit_time = exit_time
        position.exit_reason = exit_reason
        position.status = "closed"
        position.final_pnl = (exit_price - position.entry_price) * position.position_size / position.entry_price
        position.final_pnl_pct = (exit_price - position.entry_price) / position.entry_price
        
        # Update daily P&L
        self.daily_pnl += position.final_pnl
        
        # Move position from open to closed
        if position in self.open_positions:
            self.open_positions.remove(position)
        self.closed_positions.append(position)
        
        self.trading_log.append({
            "timestamp": exit_time,
            "action": "close_position",
            "message": f"Closed position for {position.symbol} at {exit_price} with P&L {position.final_pnl:.2f} USDT ({position.final_pnl_pct:.2%}). Reason: {exit_reason}"
        })
        
        return {
            "action": "position_closed",
            "position": position.model_dump(),
            "open_positions": [p.model_dump() for p in self.open_positions],
            "daily_pnl": self.daily_pnl
        }
    
    def _check_risk_limits_internal(self) -> Dict[str, Any]:
        """Check if risk limits have been exceeded"""
        # Calculate total position value
        total_position_value = sum(p.position_size for p in self.open_positions)
        
        # Check if daily loss limit exceeded
        daily_loss_limit = -1 * (self.max_daily_loss_pct / 100) * self.max_position_size
        daily_loss_exceeded = self.daily_pnl < daily_loss_limit
        
        # Check if position size limit exceeded
        position_size_exceeded = total_position_value > self.max_position_size
        
        limits_exceeded = daily_loss_exceeded or position_size_exceeded
        reason = ""
        
        if daily_loss_exceeded:
            reason = f"Daily loss limit of {daily_loss_limit:.2f} USDT exceeded. Current P&L: {self.daily_pnl:.2f} USDT"
        elif position_size_exceeded:
            reason = f"Position size limit of {self.max_position_size} USDT exceeded. Current total: {total_position_value:.2f} USDT"
        
        return {
            "limits_exceeded": limits_exceeded,
            "reason": reason,
            "daily_pnl": self.daily_pnl,
            "total_position_value": total_position_value,
            "daily_loss_limit": daily_loss_limit
        }
    
    # Tool functions for LangChain agent
    def _execute_trade(self, signal_str: str) -> str:
        """Tool function to execute a trade based on a signal"""
        try:
            # Parse signal
            signal_dict = json.loads(signal_str)
            
            # Check if we already have an open position for this symbol
            symbol = signal_dict.get("symbol", "UNKNOWN")
            existing_position = next((p for p in self.open_positions if p.symbol == symbol), None)
            
            if existing_position:
                return json.dumps({
                    "status": "error",
                    "message": f"Position already exists for {symbol}"
                })
            
            # Calculate position size based on risk parameters and confidence
            confidence = float(signal_dict.get("confidence", 0.7))
            position_size = min(self.max_position_size, self.max_position_size * confidence)
            
            # Get entry price
            entry_price = float(signal_dict.get("entry_price"))
            
            # Calculate initial stop loss
            stop_loss_pct = float(signal_dict.get("stop_loss_pct", 0.05))
            initial_stop = entry_price * (1 - stop_loss_pct)
            
            # Calculate take profit if provided
            take_profit = None
            if "take_profit_pct" in signal_dict:
                take_profit_pct = float(signal_dict.get("take_profit_pct"))
                take_profit = entry_price * (1 + take_profit_pct)
            
            # Create new position
            new_position = TradePosition(
                symbol=symbol,
                entry_price=entry_price,
                entry_time=datetime.now(),
                position_size=position_size,
                trailing_stop=initial_stop,
                take_profit=take_profit,
                initial_stop=initial_stop,
                current_price=entry_price,
                max_price_reached=entry_price
            )
            
            self.open_positions.append(new_position)
            
            self.trading_log.append({
                "timestamp": datetime.now(),
                "action": "new_position",
                "message": f"Opened new position for {symbol} at {entry_price} with size {position_size} USDT"
            })
            
            return json.dumps({
                "status": "success",
                "message": f"Position opened for {symbol} at {entry_price}",
                "position": new_position.model_dump()
            })
            
        except Exception as e:
            return json.dumps({
                "status": "error",
                "message": f"Error executing trade: {str(e)}"
            })
    
    def _update_position(self, position_str: str) -> str:
        """Tool function to update an open position"""
        try:
            # Parse position
            position_dict = json.loads(position_str)
            
            # Find position
            symbol = position_dict.get("symbol")
            position = next((p for p in self.open_positions if p.symbol == symbol), None)
            
            if not position:
                return json.dumps({
                    "status": "error",
                    "message": f"No open position found for {symbol}"
                })
            
            # Update position with current price
            current_price = float(position_dict.get("current_price"))
            current_time = datetime.now()
            
            self._update_position_internal(position, current_price, current_time)
            
            # Update trailing stop if provided
            if "new_trailing_stop" in position_dict:
                new_stop = float(position_dict.get("new_trailing_stop"))
                if new_stop > position.trailing_stop:  # Only move stop up, never down
                    position.trailing_stop = new_stop
                    self.trading_log.append({
                        "timestamp": current_time,
                        "action": "manual_update_trailing_stop",
                        "message": f"Manually updated trailing stop for {symbol} to {new_stop}"
                    })
            
            # Update take profit if provided
            if "new_take_profit" in position_dict:
                position.take_profit = float(position_dict.get("new_take_profit"))
                self.trading_log.append({
                    "timestamp": current_time,
                    "action": "update_take_profit",
                    "message": f"Updated take profit for {symbol} to {position.take_profit}"
                })
            
            return json.dumps({
                "status": "success",
                "message": f"Position updated for {symbol}",
                "position": position.model_dump()
            })
            
        except Exception as e:
            return json.dumps({
                "status": "error",
                "message": f"Error updating position: {str(e)}"
            })
    
    def _close_position(self, position_str: str) -> str:
        """Tool function to close an open position"""
        try:
            # Parse position
            position_dict = json.loads(position_str)
            
            # Find position
            symbol = position_dict.get("symbol")
            position = next((p for p in self.open_positions if p.symbol == symbol), None)
            
            if not position:
                return json.dumps({
                    "status": "error",
                    "message": f"No open position found for {symbol}"
                })
            
            # Get exit price and reason
            exit_price = float(position_dict.get("exit_price"))
            exit_reason = position_dict.get("exit_reason", "manual")
            
            # Close position
            result = self._close_position_internal(position, exit_price, datetime.now(), exit_reason)
            
            return json.dumps({
                "status": "success",
                "message": f"Position closed for {symbol} at {exit_price}",
                "position": result["position"]
            })
            
        except Exception as e:
            return json.dumps({
                "status": "error",
                "message": f"Error closing position: {str(e)}"
            })
    
    def _analyze_market(self, market_str: str) -> str:
        """Tool function to analyze current market conditions"""
        try:
            # Parse market data
            market_dict = json.loads(market_str)
            
            # Get symbol and current price
            symbol = market_dict.get("symbol")
            current_price = float(market_dict.get("current_price"))
            
            # Simple market analysis
            # In a real implementation, this would use more sophisticated analysis
            analysis = {
                "symbol": symbol,
                "current_price": current_price,
                "market_sentiment": "bullish" if np.random.random() > 0.5 else "bearish",
                "volatility": "high" if np.random.random() > 0.7 else "medium" if np.random.random() > 0.4 else "low",
                "recommendation": "hold"
            }
            
            # Generate recommendation
            if analysis["market_sentiment"] == "bullish" and analysis["volatility"] != "high":
                analysis["recommendation"] = "buy"
            elif analysis["market_sentiment"] == "bearish" and analysis["volatility"] != "low":
                analysis["recommendation"] = "sell"
            
            return json.dumps(analysis)
            
        except Exception as e:
            return json.dumps({
                "status": "error",
                "message": f"Error analyzing market: {str(e)}"
            })
    
    def _check_risk_limits(self, params_str: str) -> str:
        """Tool function to check if risk limits have been exceeded"""
        try:
            result = self._check_risk_limits_internal()
            return json.dumps(result)
            
        except Exception as e:
            return json.dumps({
                "status": "error",
                "message": f"Error checking risk limits: {str(e)}"
            })


class LiveTraderPromptTemplate(StringPromptTemplate):
    """Prompt template for the Live Trader Agent"""
    
    template: str
    input_variables: List[str]
    
    def format(self, **kwargs) -> str:
        # Get the intermediate steps (AgentAction, Observation tuples)
        # Format them in a particular way
        intermediate_steps = kwargs.pop("intermediate_steps", [])
        thoughts = ""
        
        for action, observation in intermediate_steps:
            thoughts += f"\nAction: {action.tool}\nAction Input: {action.tool_input}\nObservation: {observation}\n"

        kwargs["agent_scratchpad"] = thoughts

        # Format tools
        kwargs["tools"] = "\n".join([f"{tool.name}: {tool.description}" for tool in kwargs["tools"]])

        return self.template.format(**kwargs)


class LiveTraderOutputParser(AgentOutputParser):
    """Output parser for the Live Trader Agent"""

    def parse(self, llm_output: str) -> Union[AgentAction, AgentFinish]:
        # Check if the output indicates the agent is finished
        if "Final Answer:" in llm_output:
            return AgentFinish(
                return_values={"output": llm_output.split("Final Answer:")[-1].strip()},
                log=llm_output
            )

        # Parse out the action and action input
        regex = r"Action: (.*?)[\n]*Action Input: (.*)"
        match = re.search(regex, llm_output, re.DOTALL)

        if not match:
            raise ValueError(f"Could not parse LLM output: `{llm_output}`")

        action = match.group(1).strip()
        action_input = match.group(2).strip()

        return AgentAction(tool=action, tool_input=action_input, log=llm_output)