import pandas as pd
import numpy as np
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Tuple, Any, Union
from pydantic import BaseModel, Field
import json
import os
from langchain.agents import Tool, AgentExecutor, LLMSingleActionAgent, AgentOutputParser
from langchain.prompts import StringPromptTemplate
from langchain.schema import AgentAction, AgentFinish
from langchain.chains import LLMChain
from langchain.chat_models import ChatOpenAI
from langchain.tools import BaseTool
from langchain.schema import AgentAction, AgentFinish
from langchain.memory import ConversationBufferMemory
from langchain.callbacks.manager import CallbackManagerForToolRun
from langchain.callbacks.base import BaseCallbackHandler
import re
from trading_agent import MarketData, TrailingStopConfig, StrategyParameters, TradingAgent
from critique_agent import TradePerformance, ParameterRecommendation, CritiqueAgent
from strategy_trainer_agent import StrategyTrainerAgent
from live_trader_agent import LiveTraderAgent, TradePosition

class MarketInsight(BaseModel):
    """Model for market insights and analysis"""
    symbol: str
    timeframe: str
    timestamp: datetime
    current_price: float
    trend_direction: str  # up, down, sideways
    trend_strength: float  # 0 to 1
    volatility: float
    support_levels: List[float]
    resistance_levels: List[float]
    market_sentiment: str  # bullish, bearish, neutral
    key_events: Optional[List[str]] = None
    recommendation: str
    confidence: float  # 0 to 1
    reasoning: str

class StrategyEvaluation(BaseModel):
    """Model for strategy evaluation results"""
    strategy_name: str
    parameters: Dict[str, Any]
    performance: Dict[str, Any]
    strengths: List[str]
    weaknesses: List[str]
    improvement_suggestions: List[str]
    market_suitability: str  # high, medium, low
    overall_rating: float  # 0 to 10
    evaluation_timestamp: datetime = Field(default_factory=datetime.now)

class MasterCritiquePromptTemplate(StringPromptTemplate):
    """Custom prompt template for the Master Critique Agent"""
    template: str
    input_variables: List[str]
    
    def format(self, **kwargs) -> str:
        # Get the intermediate steps (AgentAction, Observation tuples)
        # Format them in a particular way
        intermediate_steps = kwargs.pop("intermediate_steps", [])
        thoughts = ""
        
        for action, observation in intermediate_steps:
            thoughts += f"\nAction: {action.tool}\nAction Input: {action.tool_input}\nObservation: {observation}\n"
            
        # Set the agent_scratchpad variable to that value
        kwargs["agent_scratchpad"] = thoughts
        
        # Create a tools variable from the list of tools provided
        kwargs["tools"] = "\n".join([f"{tool.name}: {tool.description}" for tool in kwargs["tools"]])
        
        return self.template.format(**kwargs)

class MasterCritiqueOutputParser(AgentOutputParser):
    """Parser for the Master Critique Agent output"""
    def parse(self, llm_output: str) -> Union[AgentAction, AgentFinish]:
        # Check if the output indicates the agent is finished
        if "Final Answer:" in llm_output:
            return AgentFinish(
                return_values={"output": llm_output.split("Final Answer:")[-1].strip()},
                log=llm_output,
            )
        
        # Parse out the action and action input
        regex = r"Action: (.*?)[\n]*Action Input: (.*)"
        match = re.search(regex, llm_output, re.DOTALL)
        
        if not match:
            raise ValueError(f"Could not parse LLM output: `{llm_output}`")
            
        action = match.group(1).strip()
        action_input = match.group(2).strip()
        
        # Return the action and action input
        return AgentAction(tool=action, tool_input=action_input, log=llm_output)

class MasterCritiqueAgent:
    """Master agent that critiques and improves trading strategies based on performance and market conditions."""
    
    def __init__(self, openai_api_key=None):
        self.model = "gpt-4o-mini"
        self.has_api_key = openai_api_key is not None
        self.openai_api_key = openai_api_key
        self.critique_agent = CritiqueAgent(openai_api_key)
        self.market_insights = []
        self.strategy_evaluations = []
        self.improvement_history = []
        self.last_evaluation_time = None
        self.evaluation_frequency = timedelta(hours=4)  # Evaluate every 4 hours
        
        # Initialize LangChain components if API key is available
        if self.has_api_key:
            self.llm = ChatOpenAI(
                model_name=self.model,
                temperature=0.3,
                openai_api_key=self.openai_api_key
            )
            self.setup_langchain_agent()
    
    def setup_langchain_agent(self):
        """Set up the LangChain agent with tools and prompt template"""
        # Define tools for the agent
        tools = [
            Tool(
                name="AnalyzeMarketConditions",
                func=self._analyze_market_conditions,
                description="Analyzes current market conditions and provides insights"
            ),
            Tool(
                name="EvaluateStrategy",
                func=self._evaluate_strategy,
                description="Evaluates a trading strategy based on its performance and parameters"
            ),
            Tool(
                name="SuggestImprovements",
                func=self._suggest_improvements,
                description="Suggests improvements to a trading strategy based on performance and market conditions"
            ),
            Tool(
                name="CompareStrategies",
                func=self._compare_strategies,
                description="Compares multiple trading strategies to identify the best one"
            ),
            Tool(
                name="AnalyzeTradeHistory",
                func=self._analyze_trade_history,
                description="Analyzes trade history to identify patterns and areas for improvement"
            )
        ]
        
        # Define prompt template
        prompt = MasterCritiquePromptTemplate(
            template="""You are a Master Critique Agent that evaluates and improves trading strategies for cryptocurrency markets.
            Your goal is to provide insightful analysis, identify weaknesses, and suggest concrete improvements to maximize profitability.
            
            Current market conditions:
            {market_conditions}
            
            Strategy performance:
            {strategy_performance}
            
            Strategy parameters:
            {strategy_parameters}
            
            Recent trade history:
            {trade_history}
            
            Your task is to critically evaluate the current strategy, identify strengths and weaknesses, and suggest specific improvements.
            Be thorough, data-driven, and consider both technical aspects and market psychology.
            
            Available tools:
            {tools}
            
            Use the tools to analyze the strategy and market conditions. Think step by step about what information you need and how to improve the strategy.
            
            {agent_scratchpad}
            """,
            input_variables=["market_conditions", "strategy_performance", "strategy_parameters", "trade_history", "tools", "agent_scratchpad"]
        )
        
        # Set up memory
        memory = ConversationBufferMemory(memory_key="chat_history")
        
        # Create LLM chain
        llm_chain = LLMChain(llm=self.llm, prompt=prompt)
        
        # Create agent
        agent = LLMSingleActionAgent(
            llm_chain=llm_chain,
            output_parser=MasterCritiqueOutputParser(),
            stop=["\nObservation:"],
            allowed_tools=[tool.name for tool in tools]
        )
        
        # Create agent executor
        self.agent_executor = AgentExecutor.from_agent_and_tools(
            agent=agent,
            tools=tools,
            verbose=True,
            memory=memory
        )
    
    def evaluate_strategy(self, market_data: pd.DataFrame, strategy_params: StrategyParameters, 
                         trade_history: List[Dict[str, Any]], force_evaluation: bool = False) -> Dict[str, Any]:
        """Evaluate a trading strategy and suggest improvements"""
        current_time = datetime.now()
        
        # Check if it's time to evaluate
        if not force_evaluation and self.last_evaluation_time and \
           (current_time - self.last_evaluation_time) < self.evaluation_frequency:
            return {
                "status": "skipped",
                "message": "Evaluation frequency not reached",
                "last_evaluation": self.last_evaluation_time.isoformat()
            }
        
        self.last_evaluation_time = current_time
        
        # If no API key or not using LangChain, use basic evaluation
        if not self.has_api_key:
            return self._basic_evaluation(market_data, strategy_params, trade_history)
        
        # Prepare market data for agent
        agent = TradingAgent(strategy_params)
        agent_market_data = agent.prepare_market_data(market_data)
        
        # Analyze market conditions
        market_conditions = self._analyze_market_conditions_internal(agent_market_data)
        
        # Calculate performance metrics
        trades_df = pd.DataFrame(trade_history) if trade_history else pd.DataFrame()
        performance = self.critique_agent.analyze_performance(trades_df) if not trades_df.empty else None
        
        try:
            # Format data for agent
            market_conditions_str = json.dumps(market_conditions.model_dump() if market_conditions else {})
            strategy_performance_str = json.dumps(performance.model_dump() if performance else {})
            strategy_parameters_str = json.dumps(strategy_params.model_dump())
            trade_history_str = json.dumps(trade_history[-10:] if len(trade_history) > 10 else trade_history)  # Limit to last 10 trades
            
            # Run agent
            result = self.agent_executor.run(
                market_conditions=market_conditions_str,
                strategy_performance=strategy_performance_str,
                strategy_parameters=strategy_parameters_str,
                trade_history=trade_history_str
            )
            
            # Parse result to get evaluation and suggestions
            evaluation = self._parse_agent_result(result, strategy_params, performance)
            
            # Store evaluation
            self.strategy_evaluations.append(evaluation)
            
            return {
                "status": "success",
                "evaluation": evaluation.model_dump(),
                "raw_result": result
            }
            
        except Exception as e:
            print(f"Error in strategy evaluation: {str(e)}")
            return {
                "status": "error",
                "message": f"Error in strategy evaluation: {str(e)}",
                "fallback": self._basic_evaluation(market_data, strategy_params, trade_history)
            }
    
    def _basic_evaluation(self, market_data: pd.DataFrame, strategy_params: StrategyParameters, 
                        trade_history: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Basic strategy evaluation without using LangChain"""
        # Calculate performance metrics
        trades_df = pd.DataFrame(trade_history) if trade_history else pd.DataFrame()
        performance = self.critique_agent.analyze_performance(trades_df) if not trades_df.empty else None
        
        # Get performance feedback
        feedback = self.critique_agent.get_performance_feedback(performance) if performance else {
            "status": "neutral",
            "message": "Not enough data to evaluate performance.",
            "suggestions": []
        }
        
        # Create evaluation
        evaluation = StrategyEvaluation(
            strategy_name="Basic Trading Strategy",
            parameters=strategy_params.model_dump(),
            performance=performance.model_dump() if performance else {},
            strengths=["Automated trading", "Technical indicator based"],
            weaknesses=["Limited market analysis", "No sentiment analysis"],
            improvement_suggestions=feedback.get("suggestions", []),
            market_suitability="medium",
            overall_rating=7.0 if feedback.get("status") == "excellent" else \
                          6.0 if feedback.get("status") == "good" else \
                          5.0 if feedback.get("status") == "neutral" else 4.0
        )
        
        # Store evaluation
        self.strategy_evaluations.append(evaluation)
        
        return {
            "status": "success",
            "evaluation": evaluation.model_dump()
        }
    
    def _analyze_market_conditions_internal(self, market_data: MarketData) -> Optional[MarketInsight]:
        """Analyze market conditions and provide insights"""
        if not market_data or not market_data.close_prices or len(market_data.close_prices) < 20:
            return None
        
        # Get recent prices
        recent_prices = market_data.close_prices[-20:]
        recent_highs = market_data.high_prices[-20:]
        recent_lows = market_data.low_prices[-20:]
        
        # Calculate trend direction and strength
        price_change = (recent_prices[-1] - recent_prices[0]) / recent_prices[0]
        trend_direction = "up" if price_change > 0.01 else "down" if price_change < -0.01 else "sideways"
        trend_strength = min(1.0, abs(price_change) * 10)  # Scale to 0-1
        
        # Calculate volatility
        volatility = np.std(np.diff(recent_prices) / recent_prices[:-1])
        
        # Identify support and resistance levels
        support_levels = [min(recent_lows)]
        resistance_levels = [max(recent_highs)]
        
        # Determine market sentiment
        if trend_direction == "up" and trend_strength > 0.5:
            sentiment = "bullish"
        elif trend_direction == "down" and trend_strength > 0.5:
            sentiment = "bearish"
        else:
            sentiment = "neutral"
        
        # Generate recommendation
        if sentiment == "bullish" and volatility < 0.03:
            recommendation = "Consider long positions with trailing stops"
            confidence = 0.7
        elif sentiment == "bearish" and volatility < 0.03:
            recommendation = "Avoid new long positions, tighten stops on existing positions"
            confidence = 0.7
        elif volatility > 0.05:
            recommendation = "Reduce position sizes due to high volatility"
            confidence = 0.8
        else:
            recommendation = "Monitor market for clearer signals"
            confidence = 0.5
        
        return MarketInsight(
            symbol=market_data.symbol,
            timeframe=market_data.timeframe,
            timestamp=datetime.now(),
            current_price=recent_prices[-1],
            trend_direction=trend_direction,
            trend_strength=trend_strength,
            volatility=volatility,
            support_levels=support_levels,
            resistance_levels=resistance_levels,
            market_sentiment=sentiment,
            recommendation=recommendation,
            confidence=confidence,
            reasoning=f"Based on {trend_direction} trend with {trend_strength:.2f} strength and {volatility:.4f} volatility"
        )
    
    def _parse_agent_result(self, result: str, strategy_params: StrategyParameters, 
                           performance: Optional[TradePerformance]) -> StrategyEvaluation:
        """Parse the result from the agent to get evaluation and suggestions"""
        try:
            # Try to extract JSON from the result
            json_match = re.search(r'\{.*\}', result, re.DOTALL)
            if json_match:
                eval_dict = json.loads(json_match.group(0))
            else:
                # If no JSON found, create basic evaluation
                return StrategyEvaluation(
                    strategy_name="Trading Strategy",
                    parameters=strategy_params.model_dump(),
                    performance=performance.model_dump() if performance else {},
                    strengths=["Automated trading"],
                    weaknesses=["Limited market analysis"],
                    improvement_suggestions=["Consider adding more technical indicators"],
                    market_suitability="medium",
                    overall_rating=5.0
                )
            
            # Create evaluation from parsed JSON
            return StrategyEvaluation(
                strategy_name=eval_dict.get("strategy_name", "Trading Strategy"),
                parameters=strategy_params.model_dump(),
                performance=performance.model_dump() if performance else {},
                strengths=eval_dict.get("strengths", []),
                weaknesses=eval_dict.get("weaknesses", []),
                improvement_suggestions=eval_dict.get("improvement_suggestions", []),
                market_suitability=eval_dict.get("market_suitability", "medium"),
                overall_rating=float(eval_dict.get("overall_rating", 5.0))
            )
            
        except Exception as e:
            print(f"Error parsing agent result: {str(e)}")
            return StrategyEvaluation(
                strategy_name="Trading Strategy",
                parameters=strategy_params.model_dump(),
                performance=performance.model_dump() if performance else {},
                strengths=["Automated trading"],
                weaknesses=["Limited market analysis"],
                improvement_suggestions=["Consider adding more technical indicators"],
                market_suitability="medium",
                overall_rating=5.0
            )
    
    # Tool functions for LangChain agent
    def _analyze_market_conditions(self, market_str: str) -> str:
        """Tool function to analyze market conditions"""
        try:
            # Parse market data
            market_dict = json.loads(market_str)
            
            # Create market insight
            insight = MarketInsight(
                symbol=market_dict.get("symbol", "UNKNOWN"),
                timeframe=market_dict.get("timeframe", "UNKNOWN"),
                timestamp=datetime.now(),
                current_price=float(market_dict.get("current_price", 0)),
                trend_direction=market_dict.get("trend_direction", "sideways"),
                trend_strength=float(market_dict.get("trend_strength", 0.5)),
                volatility=float(market_dict.get("volatility", 0.02)),
                support_levels=[float(level) for level in market_dict.get("support_levels", [])],
                resistance_levels=[float(level) for level in market_dict.get("resistance_levels", [])],
                market_sentiment=market_dict.get("market_sentiment", "neutral"),
                recommendation=market_dict.get("recommendation", "Monitor market"),
                confidence=float(market_dict.get("confidence", 0.5)),
                reasoning=market_dict.get("reasoning", "")
            )
            
            # Store insight
            self.market_insights.append(insight)
            
            # Add additional analysis
            analysis = {
                "insight": insight.model_dump(),
                "additional_analysis": {
                    "price_to_support_ratio": insight.current_price / min(insight.support_levels) if insight.support_levels else 1.0,
                    "price_to_resistance_ratio": insight.current_price / max(insight.resistance_levels) if insight.resistance_levels else 1.0,
                    "volatility_classification": "high" if insight.volatility > 0.03 else "medium" if insight.volatility > 0.01 else "low",
                    "market_phase": "accumulation" if insight.trend_direction == "sideways" and insight.market_sentiment == "neutral" else \
                                    "markup" if insight.trend_direction == "up" else \
                                    "distribution" if insight.trend_direction == "sideways" and insight.market_sentiment == "bearish" else \
                                    "markdown" if insight.trend_direction == "down" else "unknown"
                }
            }
            
            return json.dumps(analysis)
            
        except Exception as e:
            return json.dumps({
                "status": "error",
                "message": f"Error analyzing market conditions: {str(e)}"
            })
    
    def _evaluate_strategy(self, strategy_str: str) -> str:
        """Tool function to evaluate a trading strategy"""
        try:
            # Parse strategy data
            strategy_dict = json.loads(strategy_str)
            
            # Extract parameters and performance
            parameters = strategy_dict.get("parameters", {})
            performance = strategy_dict.get("performance", {})
            
            # Calculate additional metrics
            win_rate = performance.get("win_rate", 0)
            avg_profit = performance.get("avg_profit", 0)
            avg_loss = performance.get("avg_loss", 0)
            profit_factor = abs(avg_profit) / abs(avg_loss) if avg_loss and avg_loss != 0 else float('inf')
            
            # Evaluate strategy components
            ema_evaluation = "Good" if 20 <= parameters.get("ema_span", 0) <= 100 else "Too short" if parameters.get("ema_span", 0) < 20 else "Too long"
            entry_evaluation = "Good" if 0.005 <= parameters.get("entry_threshold", 0) <= 0.02 else "Too aggressive" if parameters.get("entry_threshold", 0) < 0.005 else "Too conservative"
            stop_evaluation = "Good" if 0.02 <= parameters.get("trailing_stop", {}).get("initial_distance", 0) <= 0.1 else "Too tight" if parameters.get("trailing_stop", {}).get("initial_distance", 0) < 0.02 else "Too loose"
            
            # Overall rating
            rating = 0.0
            if win_rate > 0.6 and profit_factor > 2.0:
                rating = 9.0
            elif win_rate > 0.5 and profit_factor > 1.5:
                rating = 8.0
            elif win_rate > 0.5 and profit_factor > 1.0:
                rating = 7.0
            elif win_rate > 0.4 and profit_factor > 1.0:
                rating = 6.0
            else:
                rating = 5.0
            
            # Create evaluation
            evaluation = {
                "strategy_name": "Trading Strategy",
                "parameter_evaluation": {
                    "ema_span": ema_evaluation,
                    "entry_threshold": entry_evaluation,
                    "trailing_stop": stop_evaluation
                },
                "performance_metrics": {
                    "win_rate": win_rate,
                    "profit_factor": profit_factor,
                    "avg_profit": avg_profit,
                    "avg_loss": avg_loss
                },
                "strengths": [
                    "Automated trading",
                    "Technical indicator based" if parameters.get("ema_span") else "Price action based",
                    "Good win rate" if win_rate > 0.5 else "",
                    "Good profit factor" if profit_factor > 1.5 else ""
                ],
                "weaknesses": [
                    "Limited market analysis" if not parameters.get("use_market_data", False) else "",
                    "No sentiment analysis",
                    "Poor win rate" if win_rate < 0.4 else "",
                    "Poor profit factor" if profit_factor < 1.0 else ""
                ],
                "overall_rating": rating
            }
            
            # Filter out empty strings
            evaluation["strengths"] = [s for s in evaluation["strengths"] if s]
            evaluation["weaknesses"] = [w for w in evaluation["weaknesses"] if w]
            
            return json.dumps(evaluation)
            
        except Exception as e:
            return json.dumps({
                "status": "error",
                "message": f"Error evaluating strategy: {str(e)}"
            })
    
    def _suggest_improvements(self, strategy_str: str) -> str:
        """Tool function to suggest improvements to a trading strategy"""
        try:
            # Parse strategy data
            strategy_dict = json.loads(strategy_str)
            
            # Extract parameters and performance
            parameters = strategy_dict.get("parameters", {})
            performance = strategy_dict.get("performance", {})
            market_conditions = strategy_dict.get("market_conditions", {})
            
            # Calculate metrics
            win_rate = performance.get("win_rate", 0)
            avg_profit = performance.get("avg_profit", 0)
            avg_loss = performance.get("avg_loss", 0)
            profit_factor = abs(avg_profit) / abs(avg_loss) if avg_loss and avg_loss != 0 else float('inf')
            
            # Generate suggestions based on performance and parameters
            suggestions = []
            parameter_recommendations = {}
            
            # EMA span suggestions
            ema_span = parameters.get("ema_span", 0)
            if ema_span < 20:
                suggestions.append("Increase EMA span to at least 20 for more stable signals")
                parameter_recommendations["ema_span"] = 20
            elif ema_span > 100:
                suggestions.append("Decrease EMA span to maximum 100 to be more responsive to market changes")
                parameter_recommendations["ema_span"] = 100
            
            # Entry threshold suggestions
            entry_threshold = parameters.get("entry_threshold", 0)
            if entry_threshold < 0.005:
                suggestions.append("Increase entry threshold to at least 0.005 to reduce false signals")
                parameter_recommendations["entry_threshold"] = 0.005
            elif entry_threshold > 0.02:
                suggestions.append("Decrease entry threshold to maximum 0.02 to catch more opportunities")
                parameter_recommendations["entry_threshold"] = 0.02
            
            # Trailing stop suggestions
            trailing_stop = parameters.get("trailing_stop", {})
            initial_distance = trailing_stop.get("initial_distance", 0)
            if initial_distance < 0.02:
                suggestions.append("Increase trailing stop initial distance to at least 0.02 to avoid premature exits")
                parameter_recommendations["trailing_stop"] = {"initial_distance": 0.02}
            elif initial_distance > 0.1:
                suggestions.append("Decrease trailing stop initial distance to maximum 0.1 to protect profits")
                parameter_recommendations["trailing_stop"] = {"initial_distance": 0.1}
            
            # Performance-based suggestions
            if win_rate < 0.4:
                suggestions.append("Improve entry criteria to increase win rate")
                suggestions.append("Consider adding additional confirmation indicators")
            
            if profit_factor < 1.0:
                suggestions.append("Adjust position sizing to improve profit factor")
                suggestions.append("Tighten trailing stops to capture more profits")
            
            # Market condition based suggestions
            market_sentiment = market_conditions.get("market_sentiment", "")
            volatility = market_conditions.get("volatility", 0)
            
            if market_sentiment == "bullish" and volatility < 0.03:
                suggestions.append("Optimize for long positions in current bullish market")
            elif market_sentiment == "bearish" and volatility < 0.03:
                suggestions.append("Add short position capability or focus on capital preservation")
            elif volatility > 0.05:
                suggestions.append("Reduce position sizes and widen stops in high volatility market")
            
            # Create improvement recommendations
            improvements = {
                "suggestions": suggestions,
                "parameter_recommendations": parameter_recommendations,
                "priority_improvements": suggestions[:3] if len(suggestions) > 3 else suggestions,
                "expected_impact": "high" if len(suggestions) > 5 else "medium" if len(suggestions) > 2 else "low"
            }
            
            # Store in improvement history
            self.improvement_history.append({
                "timestamp": datetime.now().isoformat(),
                "improvements": improvements
            })
            
            return json.dumps(improvements)
            
        except Exception as e:
            return json.dumps({
                "status": "error",
                "message": f"Error suggesting improvements: {str(e)}"
            })
    
    def _compare_strategies(self, strategies_str: str) -> str:
        """Tool function to compare multiple trading strategies"""
        try:
            # Parse strategies data
            strategies_dict = json.loads(strategies_str)
            strategies = strategies_dict.get("strategies", [])
            
            if not strategies:
                return json.dumps({
                    "status": "error",
                    "message": "No strategies provided for comparison"
                })
            
            # Compare strategies
            comparison_results = []
            best_strategy_index = 0
            best_score = 0
            
            for i, strategy in enumerate(strategies):
                # Extract data
                name = strategy.get("name", f"Strategy {i+1}")
                parameters = strategy.get("parameters", {})
                performance = strategy.get("performance", {})
                
                # Calculate score
                win_rate = performance.get("win_rate", 0)
                profit_factor = performance.get("profit_factor", 0)
                max_drawdown = performance.get("max_drawdown", 1)
                
                # Simple scoring formula
                score = (win_rate * 0.3) + (profit_factor * 0.5) + ((1 - abs(max_drawdown)) * 0.2)
                
                # Track best strategy
                if score > best_score:
                    best_score = score
                    best_strategy_index = i
                
                # Add to results
                comparison_results.append({
                    "name": name,
                    "score": score,
                    "key_metrics": {
                        "win_rate": win_rate,
                        "profit_factor": profit_factor,
                        "max_drawdown": max_drawdown
                    },
                    "strengths": strategy.get("strengths", []),
                    "weaknesses": strategy.get("weaknesses", [])
                })
            
            # Create comparison result
            comparison = {
                "strategies": comparison_results,
                "best_strategy": comparison_results[best_strategy_index]["name"],
                "best_strategy_score": comparison_results[best_strategy_index]["score"],
                "comparison_factors": ["win_rate", "profit_factor", "max_drawdown"],
                "recommendation": f"Based on the comparison, {comparison_results[best_strategy_index]['name']} is the best performing strategy."
            }
            
            return json.dumps(comparison)
            
        except Exception as e:
            return json.dumps({
                "status": "error",
                "message": f"Error comparing strategies: {str(e)}"
            })
    
    def _analyze_trade_history(self, history_str: str) -> str:
        """Tool function to analyze trade history"""
        try:
            # Parse trade history
            history_dict = json.loads(history_str)
            trades = history_dict.get("trades", [])
            
            if not trades:
                return json.dumps({
                    "status": "error",
                    "message": "No trades provided for analysis"
                })
            
            # Analyze trades
            total_trades = len(trades)
            winning_trades = sum(1 for trade in trades if trade.get("profit", 0) > 0)
            losing_trades = total_trades - winning_trades
            win_rate = winning_trades / total_trades if total_trades > 0 else 0
            
            # Calculate profit metrics
            total_profit = sum(trade.get("profit", 0) for trade in trades)
            avg_profit = sum(trade.get("profit", 0) for trade in trades if trade.get("profit", 0) > 0) / winning_trades if winning_trades > 0 else 0
            avg_loss = sum(abs(trade.get("profit", 0)) for trade in trades if trade.get("profit", 0) < 0) / losing_trades if losing_trades > 0 else 0
            profit_factor = avg_profit / avg_loss if avg_loss > 0 else float('inf')
            
            # Analyze trade durations
            durations = []
            for trade in trades:
                entry_time = trade.get("entry_time")
                exit_time = trade.get("exit_time")
                if entry_time and exit_time:
                    try:
                        entry_dt = datetime.fromisoformat(entry_time.replace('Z', '+00:00'))
                        exit_dt = datetime.fromisoformat(exit_time.replace('Z', '+00:00'))
                        duration = (exit_dt - entry_dt).total_seconds() / 3600  # hours
                        durations.append(duration)
                    except:
                        pass
            
            avg_duration = sum(durations) / len(durations) if durations else 0
            
            # Identify patterns
            patterns = []
            
            # Check for consecutive losses
            max_consecutive_losses = 0
            current_consecutive_losses = 0
            for trade in trades:
                if trade.get("profit", 0) < 0:
                    current_consecutive_losses += 1
                else:
                    max_consecutive_losses = max(max_consecutive_losses, current_consecutive_losses)
                    current_consecutive_losses = 0
            max_consecutive_losses = max(max_consecutive_losses, current_consecutive_losses)
            
            if max_consecutive_losses >= 3:
                patterns.append(f"Pattern: {max_consecutive_losses} consecutive losing trades detected")
            
            # Check for time-based patterns
            time_of_day_results = {}
            for trade in trades:
                entry_time = trade.get("entry_time")
                if entry_time:
                    try:
                        entry_dt = datetime.fromisoformat(entry_time.replace('Z', '+00:00'))
                        hour = entry_dt.hour
                        hour_group = f"{hour//6*6}-{hour//6*6+5}"
                        
                        if hour_group not in time_of_day_results:
                            time_of_day_results[hour_group] = {"wins": 0, "losses": 0}
                            
                        if trade.get("profit", 0) > 0:
                            time_of_day_results[hour_group]["wins"] += 1
                        else:
                            time_of_day_results[hour_group]["losses"] += 1
                    except:
                        pass
            
            for hour_group, results in time_of_day_results.items():
                total = results["wins"] + results["losses"]
                if total >= 5:  # Only consider if we have enough data
                    win_rate_in_period = results["wins"] / total
                    if win_rate_in_period >= 0.7:
                        patterns.append(f"Pattern: High win rate ({win_rate_in_period:.2f}) during hours {hour_group}")
                    elif win_rate_in_period <= 0.3:
                        patterns.append(f"Pattern: Low win rate ({win_rate_in_period:.2f}) during hours {hour_group}")
            
            # Create analysis result
            analysis = {
                "total_trades": total_trades,
                "winning_trades": winning_trades,
                "losing_trades": losing_trades,
                "win_rate": win_rate,
                "total_profit": total_profit,
                "avg_profit": avg_profit,
                "avg_loss": avg_loss,
                "profit_factor": profit_factor,
                "avg_trade_duration_hours": avg_duration,
                "patterns_detected": patterns,
                "improvement_areas": [
                    "Reduce consecutive losses" if max_consecutive_losses >= 3 else "",
                    "Improve win rate" if win_rate < 0.5 else "",
                    "Increase profit factor" if profit_factor < 1.5 else "",
                    "Optimize trade duration" if avg_duration > 48 or avg_duration < 1 else ""
                ]
            }
            
            # Filter out empty strings
            analysis["improvement_areas"] = [area for area in analysis["improvement_areas"] if area]
            
            return json.dumps(analysis)
            
        except Exception as e:
            return json.dumps({
                "status": "error",
                "message": f"Error analyzing trade history: {str(e)}"
            })