import streamlit as st
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from binance.client import Client
from datetime import datetime, timedelta
import os
import time
from critique_agent import CritiqueAgent
from parameter_optimizer import ParameterOptimizer
from strategy_trainer_agent import StrategyTrainerAgent
from live_trader_agent import LiveTraderAgent
from master_critique_agent import MasterCritiqueAgent
from agent_manager import AgentManager
from trading_agent import TradingAgent, StrategyParameters, TrailingStopConfig
try:
    import plotly.graph_objects as go
    from plotly.subplots import make_subplots
    import plotly.express as px
    PLOTLY_AVAILABLE = True
except ImportError:
    PLOTLY_AVAILABLE = False
    st.warning("Plotly not installed. Install with: pip install plotly")

# Set page configuration
st.set_page_config(page_title="Crypto Trading Strategy Visualizer", layout="wide")

# Streamlit app title with styling
st.title("📈 Crypto Trading Strategy Visualizer")
st.markdown("*Analyze and visualize trading strategies with customizable parameters*")

# Create tabs for different sections
tabs = st.tabs(["Strategy Visualization", "Performance Metrics", "Live Trades", "AI Agents", "About"])

# Sidebar configuration
st.sidebar.header("Strategy Parameters")

# Live Trading Controls
st.sidebar.markdown("---")
st.sidebar.subheader("Live Trading")
live_trading_enabled = st.sidebar.checkbox("Enable Live Trading", value=False)
if live_trading_enabled:
    trading_status = st.sidebar.radio("Trading Status", ["Start Trading", "Stop Trading"], index=1)
    trading_active = trading_status == "Start Trading"

    # Live trading timeframe selection
    st.sidebar.subheader("Live Trading Timeframe")
    live_timeframe_options = {
        "1 Day": "1d",
        "4 Hours": "4h",
        "1 Hour": "1h",
        "15 Minutes": "15m",
        "5 Minutes": "5m",
        "1 Minute": "1m"
    }
    live_timeframe = st.sidebar.selectbox(
        "Select Live Trading Timeframe",
        list(live_timeframe_options.keys()),
        index=2,  # Default to 1 Hour
        key="live_timeframe_select"
    )
    live_timeframe_val = live_timeframe_options[live_timeframe]

    # Trading risk management
    if trading_active:
        st.sidebar.markdown("---")
        st.sidebar.subheader("Risk Management")
        max_position_size = st.sidebar.slider("Max Position Size (USDT)",
                                            min_value=10,
                                            max_value=1000,
                                            value=100,
                                            step=10)
        max_daily_loss = st.sidebar.slider("Max Daily Loss (%)",
                                          min_value=1,
                                          max_value=10,
                                          value=3,
                                          step=1)
        st.sidebar.info(f"Trading will stop if daily loss exceeds {max_daily_loss}%")
else:
    trading_active = False
    # Set default values for live trading variables even when disabled
    live_timeframe = "1 Hour"
    live_timeframe_val = "1h"

# Symbol selection
symbol = st.sidebar.selectbox(
    "Select Trading Pair",
    options=["BTCUSDT", "ETHUSDT", "BNBUSDT", "SOLUSDT", "ADAUSDT"],
    index=0
)

# Timeframe selection
timeframe_options = {
    "1 Day": "1d",
    "4 Hours": "4h",
    "1 Hour": "1h",
    "15 Minutes": "15m"
}
timeframe = st.sidebar.selectbox("Select Timeframe", list(timeframe_options.keys()), index=0)
timeframe_val = timeframe_options[timeframe]

# Date range selection with custom option
end_date = datetime.now()
start_date_options = {
    "1 Year": end_date - timedelta(days=365),
    "2 Years": end_date - timedelta(days=730),
    "3 Years": end_date - timedelta(days=1095),
    "4 Years": end_date - timedelta(days=1460),
    "5 Years": end_date - timedelta(days=1825),
    "Custom": None
}

date_range = st.sidebar.selectbox("Select Date Range", list(start_date_options.keys()), index=0)

if date_range == "Custom":
    # Custom date range input
    col1, col2 = st.sidebar.columns(2)
    with col1:
        custom_years = st.number_input("Years back", min_value=1, max_value=10, value=1, step=1)
    with col2:
        custom_months = st.number_input("Extra months", min_value=0, max_value=11, value=0, step=1)

    start_date = end_date - timedelta(days=custom_years*365 + custom_months*30)
    st.sidebar.info(f"📅 Custom: {custom_years}y {custom_months}m ({start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')})")
else:
    start_date = start_date_options[date_range]
    st.sidebar.info(f"📅 Range: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")

# Calculate data points and show optimization info
days_diff = (end_date - start_date).days
estimated_points = days_diff * (1440 // {"1d": 1440, "4h": 240, "1h": 60, "15m": 15}.get(timeframe_val, 60))
if estimated_points > 10000:
    st.sidebar.warning(f"⚡ Large dataset ({estimated_points:,} points). Using optimized loading...")
else:
    st.sidebar.success(f"📊 Dataset size: {estimated_points:,} points")

# Chart type selection
st.sidebar.markdown("---")
st.sidebar.subheader("📈 Chart Settings")
chart_type = st.sidebar.selectbox(
    "Chart Type",
    options=["Line Chart", "Candlestick Chart"] if PLOTLY_AVAILABLE else ["Line Chart"],
    index=0,
    help="Choose between line chart and candlestick chart visualization"
)

# Chart styling options
if chart_type == "Candlestick Chart" and PLOTLY_AVAILABLE:
    show_volume = st.sidebar.checkbox("Show Volume", value=True, help="Display volume bars below the price chart")
    chart_theme = st.sidebar.selectbox("Chart Theme", ["plotly", "plotly_white", "plotly_dark"], index=1)
else:
    show_volume = False
    chart_theme = "plotly_white"

# Auto-optimization toggle
st.sidebar.markdown("---")
st.sidebar.subheader("AI Agents")
auto_optimize = st.sidebar.checkbox("Enable AI Agents", value=True, help="Enable the three specialized LangChain agents for strategy optimization, live trading, and critique")
optimization_frequency = st.sidebar.selectbox(
    "Optimization Frequency",
    ["Every 4 hours", "Every 12 hours", "Every 24 hours", "After each trade"],
    index=0,
    disabled=not auto_optimize,
    help="How often to re-optimize strategy parameters"
)

# Note: Agent manager will be initialized after parameters are defined

# Agent controls
if auto_optimize:
    st.sidebar.markdown("---")
    st.sidebar.subheader("Agent Controls")
    force_training = st.sidebar.button("Force Strategy Training", help="Force the Strategy Trainer agent to optimize the trading strategy now")
    force_evaluation = st.sidebar.button("Force Strategy Evaluation", help="Force the Master Critique agent to evaluate the current strategy now")

# Strategy parameters section with manual/auto toggle
st.sidebar.subheader("Technical Indicators")
use_manual_params = st.sidebar.checkbox("Use Manual Parameters", value=not auto_optimize)

# Strategy parameters (only enabled if manual mode is selected)
ema_span = st.sidebar.slider("EMA Span", min_value=10, max_value=200, value=50, disabled=not use_manual_params)

# Trailing Stop Parameters
st.sidebar.subheader("Trailing Stop Configuration")
distance = st.sidebar.slider("Initial Trailing Distance (%)", min_value=0.1, max_value=10.0, value=5.0, step=0.1, disabled=not use_manual_params) / 100
adaptive_adjustment = st.sidebar.checkbox("Use Adaptive Trailing Stop", value=True, disabled=not use_manual_params)
volatility_factor = st.sidebar.slider("Volatility Factor", min_value=0.1, max_value=5.0, value=1.0, step=0.1, disabled=not (use_manual_params and adaptive_adjustment))
min_distance = st.sidebar.slider("Minimum Distance (%)", min_value=0.1, max_value=5.0, value=1.0, step=0.1, disabled=not (use_manual_params and adaptive_adjustment)) / 100
max_distance = st.sidebar.slider("Maximum Distance (%)", min_value=1.0, max_value=20.0, value=10.0, step=0.1, disabled=not (use_manual_params and adaptive_adjustment)) / 100

# Entry Parameters
st.sidebar.subheader("Entry Configuration")
entry_threshold = st.sidebar.slider("Entry Threshold (%)", min_value=0.1, max_value=5.0, value=1.0, step=0.1, disabled=not use_manual_params) / 100
use_ai_optimization = st.sidebar.checkbox("Use AI for Entry Optimization", value=True)

# Initialize agent manager if not in session state and auto_optimize is enabled
if 'agent_manager' not in st.session_state and auto_optimize:
    # Try to get OpenAI API key from environment variables
    openai_api_key = os.getenv("OPENAI_API_KEY")
    st.session_state.agent_manager = AgentManager(openai_api_key)

    # Initialize with default parameters
    trailing_config = TrailingStopConfig(
        initial_distance=distance,
        adaptive_adjustment=adaptive_adjustment,
        volatility_factor=volatility_factor,
        min_distance=min_distance,
        max_distance=max_distance
    )

    initial_params = StrategyParameters(
        ema_span=ema_span,
        entry_threshold=entry_threshold,
        trailing_stop=trailing_config,
        use_ai_optimization=True
    )

    st.session_state.agent_manager.initialize(initial_params)
    st.sidebar.success("AI Agents initialized successfully!")

# Initialize Binance client with better error handling
# Try to get API keys from environment variables
api_key = os.getenv("BINANCE_API_KEY")
api_secret = os.getenv("BINANCE_API_SECRET")

# Initialize client with or without API keys
try:
    if api_key and api_secret:
        client = Client(api_key, api_secret, testnet=False)
        # Test the connection
        client.ping()
        st.sidebar.success("✅ Connected to Binance API")
    else:
        # Use without authentication for demo/testing purposes
        client = Client()
        # Test the connection
        client.ping()
        st.sidebar.warning("⚠️ Running in demo mode without API keys. Some features may be limited.")
except Exception as e:
    st.sidebar.error(f"❌ Binance API connection failed: {str(e)}")
    # Create a fallback client that will use mock data
    client = None

# Function to generate mock data when API is not available
def generate_mock_data(symbol, timeframe, start_date, end_date=None):
    """
    Generate realistic mock cryptocurrency data for testing purposes.

    Parameters:
    -----------
    symbol : str
        Trading pair symbol (e.g., 'BTCUSDT')
    timeframe : str
        Timeframe for the data (e.g., '1d', '4h')
    start_date : datetime
        Start date for the data
    end_date : datetime, optional
        End date for the data, defaults to current time

    Returns:
    --------
    pd.DataFrame
        DataFrame containing mock OHLCV data in Binance format
    """
    import numpy as np

    # Set end date if not provided
    if end_date is None:
        end_date = datetime.now()

    # Calculate timeframe in minutes
    timeframe_minutes = {
        '1m': 1, '5m': 5, '15m': 15, '30m': 30,
        '1h': 60, '4h': 240, '1d': 1440, '1w': 10080
    }

    minutes = timeframe_minutes.get(timeframe, 60)

    # Generate time series
    time_range = pd.date_range(start=start_date, end=end_date, freq=f'{minutes}min')

    # Base price for different symbols
    base_prices = {
        'BTCUSDT': 45000,
        'ETHUSDT': 3000,
        'ADAUSDT': 0.5,
        'BNBUSDT': 300,
        'SOLUSDT': 100
    }

    base_price = base_prices.get(symbol, 45000)

    # Generate realistic price data with trends and volatility
    np.random.seed(42)  # For reproducible results

    # Create price movements
    returns = np.random.normal(0, 0.02, len(time_range))  # 2% daily volatility

    # Add some trend
    trend = np.linspace(0, 0.1, len(time_range))  # 10% upward trend over period
    returns += trend / len(time_range)

    # Calculate prices
    prices = [base_price]
    for ret in returns[1:]:
        new_price = prices[-1] * (1 + ret)
        prices.append(max(new_price, base_price * 0.5))  # Prevent negative prices

    # Generate OHLCV data
    data = []
    for i, timestamp in enumerate(time_range):
        close_price = prices[i]

        # Generate realistic OHLC based on close price
        volatility = abs(np.random.normal(0, 0.01))  # Intraday volatility
        high = close_price * (1 + volatility)
        low = close_price * (1 - volatility)

        # Open price is close to previous close
        if i == 0:
            open_price = close_price
        else:
            open_price = prices[i-1] * (1 + np.random.normal(0, 0.005))

        # Ensure OHLC relationships are correct
        high = max(high, open_price, close_price)
        low = min(low, open_price, close_price)

        # Generate volume (higher volume on larger price movements)
        volume = abs(np.random.normal(1000, 200)) * (1 + abs(returns[i]) * 10)

        data.append([
            int(timestamp.timestamp() * 1000),  # Timestamp in milliseconds
            str(open_price),
            str(high),
            str(low),
            str(close_price),
            str(volume),
            int(timestamp.timestamp() * 1000),  # Close time
            "0",  # Quote asset volume
            0,    # Number of trades
            "0",  # Taker buy base asset volume
            "0",  # Taker buy quote asset volume
            "0"   # Ignore
        ])

    return pd.DataFrame(data)

# Function to fetch historical data with optimized caching and parallel processing
@st.cache_data(ttl=3600, max_entries=100)  # Cache for 1 hour, store up to 100 different queries
def getdata(symbol, timeframe, start_date, end_date=None):
    """
    Fetch historical price data with optimized performance.

    Parameters:
    -----------
    symbol : str
        Trading pair symbol (e.g., 'BTCUSDT')
    timeframe : str
        Timeframe for the data (e.g., '1d', '4h')
    start_date : datetime
        Start date for the data
    end_date : datetime, optional
        End date for the data, defaults to current time

    Returns:
    --------
    pd.DataFrame
        DataFrame containing OHLCV data
    """
    # Convert start_date to string format
    start_str = start_date.strftime("%d %b, %Y")

    # Set end date if provided
    end_str = None
    if end_date:
        end_str = end_date.strftime("%d %b, %Y")

    # Create a loading placeholder
    loading_placeholder = st.empty()
    loading_placeholder.info(f"Fetching {symbol} data for {timeframe} timeframe...")

    try:
        # Check if client is available
        if client is None:
            # Use mock data if no client available
            loading_placeholder.info("Using mock data (no API connection)")
            frame = generate_mock_data(symbol, timeframe, start_date, end_date)
        else:
            # For shorter timeframes and longer periods, use chunking to improve performance
            current_time = datetime.now()
            time_diff = (current_time - start_date).days

            # For large date ranges on small timeframes, use chunking
            if (timeframe in ['15m', '5m', '1m'] and time_diff > 7) or (timeframe in ['1h', '4h'] and time_diff > 30):
                # Use chunking for better performance
                frames = []

                # Calculate chunk size based on timeframe
                if timeframe == '1m':
                    chunk_days = 1  # 1 day chunks for 1m data
                elif timeframe == '5m':
                    chunk_days = 3  # 3 day chunks for 5m data
                elif timeframe == '15m':
                    chunk_days = 5  # 5 day chunks for 15m data
                elif timeframe == '1h':
                    chunk_days = 10  # 10 day chunks for 1h data
                else:
                    chunk_days = 30  # 30 day chunks for larger timeframes

                # Create chunks
                chunk_start = start_date
                progress_bar = st.progress(0)

                while chunk_start < current_time:
                    chunk_end = min(chunk_start + timedelta(days=chunk_days), current_time)
                    chunk_start_str = chunk_start.strftime("%d %b, %Y")
                    chunk_end_str = chunk_end.strftime("%d %b, %Y")

                    # Update loading message
                    loading_placeholder.info(f"Fetching {symbol} data: {chunk_start_str} to {chunk_end_str}")

                    try:
                        # Fetch chunk with retry logic
                        chunk = pd.DataFrame(client.get_historical_klines(
                            symbol, timeframe, chunk_start_str, chunk_end_str))

                        if not chunk.empty:
                            frames.append(chunk)
                    except Exception as chunk_error:
                        st.warning(f"Failed to fetch chunk {chunk_start_str} to {chunk_end_str}: {str(chunk_error)}")
                        continue

                    # Update progress
                    progress = min(1.0, (chunk_end - start_date).total_seconds() / (current_time - start_date).total_seconds())
                    progress_bar.progress(progress)

                    # Move to next chunk
                    chunk_start = chunk_end

                # Clear progress indicators
                progress_bar.empty()

                # Combine chunks
                if frames:
                    frame = pd.concat(frames)
                else:
                    # If no chunks were successful, use mock data
                    loading_placeholder.warning("Failed to fetch real data, using mock data")
                    frame = generate_mock_data(symbol, timeframe, start_date, end_date)
            else:
                # For smaller datasets, fetch all at once
                try:
                    frame = pd.DataFrame(client.get_historical_klines(symbol, timeframe, start_str, end_str))
                except Exception as fetch_error:
                    loading_placeholder.warning(f"Failed to fetch real data: {str(fetch_error)}, using mock data")
                    frame = generate_mock_data(symbol, timeframe, start_date, end_date)

        # Clear loading message
        loading_placeholder.empty()

        # Check if we have data
        if frame.empty:
            st.error(f"No data available for {symbol} with the selected parameters.")
            return pd.DataFrame()

        # Process the data
        frame = frame.iloc[:,:6]
        frame.columns = ["Time","Open","High","Low","Close","Volume"]
        frame.set_index("Time", inplace=True)
        frame.index = pd.to_datetime(frame.index, unit="ms")
        frame = frame.astype(float)

        # Add a success message
        st.success(f"Successfully loaded {len(frame)} data points for {symbol}")

        return frame
    except Exception as e:
        loading_placeholder.empty()
        st.error(f"Error fetching data: {str(e)}")
        return pd.DataFrame()

# Function to create candlestick chart
def create_candlestick_chart(df, buy_points=None, sell_points=None, indicators=None, title="Price Chart", show_volume=True, theme="plotly_white"):
    """
    Create an interactive candlestick chart with buy/sell signals and technical indicators.

    Parameters:
    -----------
    df : pandas.DataFrame
        DataFrame with OHLCV data
    buy_points : list, optional
        List of (timestamp, price) tuples for buy signals
    sell_points : list, optional
        List of (timestamp, price) tuples for sell signals
    indicators : dict, optional
        Dictionary of technical indicators to plot
    title : str
        Chart title
    show_volume : bool
        Whether to show volume subplot
    theme : str
        Plotly theme to use

    Returns:
    --------
    plotly.graph_objects.Figure
        Interactive candlestick chart
    """
    if not PLOTLY_AVAILABLE:
        st.error("Plotly is required for candlestick charts. Please install with: pip install plotly")
        return None

    # Create subplots
    if show_volume:
        fig = make_subplots(
            rows=2, cols=1,
            shared_xaxes=True,
            vertical_spacing=0.1,
            subplot_titles=(title, 'Volume'),
            row_heights=[0.7, 0.3]
        )
    else:
        fig = go.Figure()

    # Add candlestick chart
    candlestick = go.Candlestick(
        x=df.index,
        open=df['Open'],
        high=df['High'],
        low=df['Low'],
        close=df['Close'],
        name="Price",
        increasing_line_color='#00ff00',  # Green for bullish candles
        decreasing_line_color='#ff0000',  # Red for bearish candles
        increasing_fillcolor='rgba(0, 255, 0, 0.3)',
        decreasing_fillcolor='rgba(255, 0, 0, 0.3)'
    )

    if show_volume:
        fig.add_trace(candlestick, row=1, col=1)
    else:
        fig.add_trace(candlestick)

    # Add technical indicators
    if indicators:
        for indicator_name, indicator_data in indicators.items():
            if indicator_name.startswith('EMA') or indicator_name.startswith('SMA'):
                # Moving averages on main chart
                fig.add_trace(
                    go.Scatter(
                        x=df.index,
                        y=indicator_data,
                        mode='lines',
                        name=indicator_name,
                        line=dict(width=2)
                    ),
                    row=1, col=1
                )
            elif indicator_name in ['Upper_Band', 'Lower_Band']:
                # Bollinger Bands
                fig.add_trace(
                    go.Scatter(
                        x=df.index,
                        y=indicator_data,
                        mode='lines',
                        name=indicator_name,
                        line=dict(width=1, dash='dash'),
                        opacity=0.7
                    ),
                    row=1, col=1
                )

    # Add buy signals
    if buy_points:
        buy_x, buy_y = zip(*buy_points) if buy_points else ([], [])
        fig.add_trace(
            go.Scatter(
                x=buy_x,
                y=buy_y,
                mode='markers',
                name='Buy Signal',
                marker=dict(
                    symbol='triangle-up',
                    size=12,
                    color='green',
                    line=dict(width=2, color='darkgreen')
                )
            ),
            row=1, col=1
        )

    # Add sell signals
    if sell_points:
        sell_x, sell_y = zip(*sell_points) if sell_points else ([], [])
        fig.add_trace(
            go.Scatter(
                x=sell_x,
                y=sell_y,
                mode='markers',
                name='Sell Signal',
                marker=dict(
                    symbol='triangle-down',
                    size=12,
                    color='red',
                    line=dict(width=2, color='darkred')
                )
            ),
            row=1, col=1
        )

    # Add volume bars
    if show_volume and 'Volume' in df.columns:
        # Color volume bars based on price movement
        colors = ['green' if close >= open else 'red'
                 for close, open in zip(df['Close'], df['Open'])]

        fig.add_trace(
            go.Bar(
                x=df.index,
                y=df['Volume'],
                name='Volume',
                marker_color=colors,
                opacity=0.7
            ),
            row=2, col=1
        )

    # Update layout
    fig.update_layout(
        template=theme,
        title=title,
        xaxis_title="Date",
        yaxis_title="Price",
        height=600 if show_volume else 500,
        showlegend=True,
        xaxis_rangeslider_visible=False,  # Hide range slider for cleaner look
        hovermode='x unified'
    )

    # Update y-axis for volume
    if show_volume:
        fig.update_yaxes(title_text="Volume", row=2, col=1)

    return fig

# Function to create line chart (fallback for when plotly is not available)
def create_line_chart(df, buy_points=None, sell_points=None, indicators=None, title="Price Chart"):
    """
    Create a matplotlib line chart as fallback when plotly is not available.
    """
    fig, ax = plt.subplots(figsize=(12, 8))

    # Plot price line
    ax.plot(df.index, df['Close'], label='Close Price', linewidth=2, color='blue')

    # Add technical indicators
    if indicators:
        colors = ['orange', 'purple', 'brown', 'pink', 'gray']
        for i, (indicator_name, indicator_data) in enumerate(indicators.items()):
            if indicator_name.startswith('EMA') or indicator_name.startswith('SMA'):
                ax.plot(df.index, indicator_data, label=indicator_name,
                       linewidth=1.5, color=colors[i % len(colors)])

    # Add buy/sell signals
    if buy_points:
        buy_x, buy_y = zip(*buy_points) if buy_points else ([], [])
        ax.scatter(buy_x, buy_y, color='green', marker='^', s=100, label='Buy Signal', zorder=5)

    if sell_points:
        sell_x, sell_y = zip(*sell_points) if sell_points else ([], [])
        ax.scatter(sell_x, sell_y, color='red', marker='v', s=100, label='Sell Signal', zorder=5)

    # Formatting
    ax.set_title(title, fontsize=16, fontweight='bold')
    ax.set_xlabel('Date', fontsize=12)
    ax.set_ylabel('Price', fontsize=12)
    ax.legend()
    ax.grid(True, alpha=0.3)

    # Format x-axis dates
    ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
    ax.xaxis.set_major_locator(mdates.MonthLocator(interval=1))
    plt.xticks(rotation=45)

    plt.tight_layout()
    return fig

# Trading agent components are imported at the top of the file

# Function to implement AI-driven trading strategy
def ai_trading_strategy(df, entry_threshold, stop_loss_distance, ema_col, use_agent=True, trailing_config=None, take_profit_level=None):
    """
    Implements an AI-driven trading strategy that uses multiple technical indicators
    to make buy/sell decisions. The AI agent controls the entry and exit points.

    Parameters:
    -----------
    df : pandas.DataFrame
        DataFrame containing price data and technical indicators
    entry_threshold : float
        Minimum return threshold for entry consideration
    stop_loss_distance : float
        Initial trailing stop distance as a percentage
    ema_col : str
        Name of the EMA column to use
    use_agent : bool
        Whether to use the AI agent for decision making
    trailing_config : TrailingStopConfig
        Configuration for the trailing stop
    take_profit_level : float
        Take profit level as a percentage of entry price

    Returns:
    --------
    buy_points : list
        List of (time, price) tuples for buy signals
    sell_points : list
        List of (time, price) tuples for sell signals
    trade_results : list
        List of dictionaries containing trade results
    """
    buy_points = []  # List to store buy points
    sell_points = []  # List to store sell points
    trade_results = []  # List to store trade results (profit/loss)
    in_position = False
    entry_price = 0
    trailing_stop = 0

    # Create trading agent
    strategy_params = StrategyParameters(
        ema_span=int(ema_col.split('_')[1]),
        entry_threshold=entry_threshold,
        trailing_stop=trailing_config,
        use_ai_optimization=True
    )
    agent = TradingAgent(strategy_params)

    for i, (index, row) in enumerate(df.iterrows()):
        # Skip the first 20 rows to allow indicators to stabilize
        if i < 20:
            continue

        # If not in a position, check for entry signals
        if not in_position:
            # Prepare market data for agent
            temp_df = df.iloc[:i+1].copy()
            market_data = agent.prepare_market_data(temp_df)

            # Get entry signal from agent
            signal = agent.get_entry_signal(market_data)

            # AI-driven entry decision
            if signal and signal.confidence > 0.5:
                # Additional confirmation using multiple indicators
                rsi_bullish = row['RSI'] > 30 and row['RSI'] < 70  # Not overbought or oversold
                macd_bullish = row['MACD'] > row['MACD_Signal']  # MACD above signal line
                stoch_bullish = row['Stoch_K'] > row['Stoch_D'] and row['Stoch_K'] < 80  # Stochastic K above D and not overbought
                price_above_ema = row.Close > row[ema_col]  # Price above EMA

                # Count how many indicators are bullish
                bullish_count = sum([rsi_bullish, macd_bullish, stoch_bullish, price_above_ema])

                # Enter position if majority of indicators are bullish
                if bullish_count >= 3 or (bullish_count >= 2 and signal.confidence > 0.7):
                    entry_price = row.Close
                    in_position = True

                    # Use agent's recommended trailing stop if available
                    ai_analysis = agent._analyze_with_llm(market_data)
                    if ai_analysis and 'recommended_stop' in ai_analysis:
                        stop_distance = ai_analysis['recommended_stop']
                    else:
                        stop_distance = stop_loss_distance

                    trailing_stop = entry_price * (1 - stop_distance)
                    buy_points.append((index, entry_price))

                    # Store the indicators that triggered the entry
                    entry_indicators = {
                        'rsi': row['RSI'],
                        'macd': row['MACD'],
                        'macd_signal': row['MACD_Signal'],
                        'stoch_k': row['Stoch_K'],
                        'stoch_d': row['Stoch_D'],
                        'price_ema_ratio': row.Close / row[ema_col],
                        'confidence': signal.confidence,
                        'reason': signal.reason
                    }

        # If in a position, check for exit conditions
        elif in_position:
            # Update trailing stop if price moves in favorable direction
            if trailing_config and trailing_config.adaptive_adjustment:
                # Prepare market data for agent
                temp_df = df.iloc[:i+1].copy()
                market_data = agent.prepare_market_data(temp_df)
                optimal_distance = agent.optimize_trailing_stop(market_data)
                potential_new_stop = row.Close * (1 - optimal_distance)
            else:
                potential_new_stop = row.Close * (1 - stop_loss_distance)

            if potential_new_stop > trailing_stop:
                trailing_stop = potential_new_stop

            # Check exit conditions
            take_profit_price = entry_price * (1 + take_profit_level) if take_profit_level else float('inf')

            # AI-driven exit decision
            exit_signal = False

            # Check technical indicators for exit signals
            rsi_bearish = row['RSI'] > 70  # Overbought
            macd_bearish = row['MACD'] < row['MACD_Signal']  # MACD below signal line
            stoch_bearish = row['Stoch_K'] < row['Stoch_D'] and row['Stoch_K'] > 80  # Stochastic K below D and overbought
            price_below_ema = row.Close < row[ema_col]  # Price below EMA

            # Count how many indicators are bearish
            bearish_count = sum([rsi_bearish, macd_bearish, stoch_bearish, price_below_ema])

            # Exit if price hits trailing stop, take profit, or majority of indicators are bearish
            if (row.Close <= trailing_stop or  # Trailing stop hit
                row.Close >= take_profit_price or  # Take profit level hit
                (bearish_count >= 3 and row.Close > entry_price * 1.01)):  # Majority bearish and in profit

                exit_price = row.Close
                sell_points.append((index, exit_price))

                # Calculate trade result
                profit_pct = (exit_price - entry_price) / entry_price
                trade_results.append({
                    'entry_date': buy_points[-1][0],
                    'exit_date': index,
                    'entry_price': entry_price,
                    'exit_price': exit_price,
                    'profit_pct': profit_pct,
                    'profit_abs': exit_price - entry_price,
                    'exit_reason': 'stop_loss' if row.Close <= trailing_stop else
                                  'take_profit' if row.Close >= take_profit_price else
                                  'indicators'
                })

                in_position = False
                trailing_stop = 0  # Reset trailing stop for next trade

    # Handle open position at the end of the period
    if in_position and len(buy_points) > len(sell_points):
        last_price = df.iloc[-1].Close
        profit_pct = (last_price - entry_price) / entry_price
        trade_results.append({
            'entry_date': buy_points[-1][0],
            'exit_date': df.index[-1],
            'entry_price': entry_price,
            'exit_price': last_price,
            'profit_pct': profit_pct,
            'profit_abs': last_price - entry_price,
            'status': 'open',
            'exit_reason': 'end_of_period'
        })

    return buy_points, sell_points, trade_results

# Function to implement trailing stop strategy with EMA filter (legacy version)
def trail_with_ema(df, entry, dist, ema_col, use_agent=False, trailing_config=None, take_profit_level=None):
    buy_points = []  # List to store buy points
    sell_points = []  # List to store sell points
    trade_results = []  # List to store trade results (profit/loss)
    in_position = False
    entry_price = 0
    trailing_stop = 0

    # Create trading agent if AI optimization is enabled
    agent = None
    if use_agent:
        strategy_params = StrategyParameters(
            ema_span=int(ema_col.split('_')[1]),
            entry_threshold=entry,
            trailing_stop=trailing_config,
            use_ai_optimization=True
        )
        agent = TradingAgent(strategy_params)

    for index, row in df.iterrows():
        if not in_position and row.ret > entry:
            # Basic condition
            basic_condition = row.Close > row[ema_col]

            # If agent is available, use it for enhanced entry signal
            if agent and len(df.loc[:index]) > 20:  # Need some history for the agent
                # Prepare market data for agent
                temp_df = df.loc[:index].copy()
                market_data = agent.prepare_market_data(temp_df)

                # Get entry signal from agent
                signal = agent.get_entry_signal(market_data)

                if signal and signal.confidence > 0.5:
                    entry_price = row.price
                    in_position = True

                    # Use agent's recommended trailing stop if available
                    ai_analysis = agent._analyze_with_llm(market_data)
                    if ai_analysis and 'recommended_stop' in ai_analysis:
                        stop_distance = ai_analysis['recommended_stop']
                    else:
                        stop_distance = dist

                    trailing_stop = entry_price * (1 - stop_distance)
                    buy_points.append((index, entry_price))
                    st.sidebar.success(f"AI Entry Signal: {signal.confidence:.2f} confidence")
                    st.sidebar.info(f"Reason: {signal.reason[:100]}...")

            # Fall back to basic condition if no agent or agent didn't give signal
            elif basic_condition:
                entry_price = row.price
                in_position = True
                trailing_stop = entry_price * (1 - dist)  # Calculate trailing stop as percentage below entry price
                buy_points.append((index, entry_price))  # Add buy point to list

        elif in_position:
            # Update trailing stop if price moves in favorable direction
            if agent and trailing_config.adaptive_adjustment:
                # Calculate adaptive trailing stop based on recent volatility
                temp_df = df.loc[:index].copy()
                market_data = agent.prepare_market_data(temp_df)
                optimal_distance = agent.optimize_trailing_stop(market_data)
                potential_new_stop = row.Close * (1 - optimal_distance)
            else:
                potential_new_stop = row.Close * (1 - dist)

            if potential_new_stop > trailing_stop:
                trailing_stop = potential_new_stop

            # Check exit conditions
            # Exit if price hits trailing stop, falls below EMA, or hits take-profit level
            take_profit_price = entry_price * (1 + take_profit_level) if take_profit_level else float('inf')
            if (row.Close <= trailing_stop or  # Trailing stop hit
                row.Close < row[ema_col] or    # Price below EMA
                row.Close >= take_profit_price):  # Take profit level hit
                exit_price = row.price
                sell_points.append((index, exit_price))  # Add sell point to list

                # Calculate trade result
                profit_pct = (exit_price - entry_price) / entry_price
                trade_results.append({
                    'entry_date': buy_points[-1][0],
                    'exit_date': index,
                    'entry_price': entry_price,
                    'exit_price': exit_price,
                    'profit_pct': profit_pct,
                    'profit_abs': exit_price - entry_price
                })

                in_position = False
                trailing_stop = 0  # Reset trailing stop for next trade

    # Handle open position at the end of the period
    if in_position and len(buy_points) > len(sell_points):
        last_price = df.iloc[-1].Close
        profit_pct = (last_price - entry_price) / entry_price
        trade_results.append({
            'entry_date': buy_points[-1][0],
            'exit_date': df.index[-1],
            'entry_price': entry_price,
            'exit_price': last_price,
            'profit_pct': profit_pct,
            'profit_abs': last_price - entry_price,
            'status': 'open'
        })

    return buy_points, sell_points, trade_results

# Initialize parameter optimizer and critique agent
if 'parameter_optimizer' not in st.session_state:
    st.session_state.parameter_optimizer = ParameterOptimizer()

if 'critique_agent' not in st.session_state:
    openai_api_key = os.getenv("OPENAI_API_KEY")
    st.session_state.critique_agent = CritiqueAgent(openai_api_key=openai_api_key)

# Initialize random for demo purposes
import random

# Function to calculate technical indicators with optimized performance
@st.cache_data(ttl=3600)  # Cache for 1 hour
def calculate_indicators(df, params=None, selected_indicators=None):
    """
    Calculate technical indicators for the dataframe with optimized performance.

    Parameters:
    -----------
    df : pandas.DataFrame
        DataFrame containing price data
    params : dict, optional
        Dictionary of parameters for the indicators
    selected_indicators : list, optional
        List of indicators to calculate. If None, calculates all indicators.

    Returns:
    --------
    pandas.DataFrame
        DataFrame with added technical indicators
    """
    # Create a copy to avoid modifying the original
    df = df.copy()

    # Use provided parameters or fall back to global ones
    if params is None:
        params = {
            'ema_span': ema_span,
            'rsi_period': 14,
            'macd_fast': 12,
            'macd_slow': 26,
            'macd_signal': 9,
            'bb_period': 20,
            'bb_std': 2,
            'sma_period': 200,
            'stoch_k': 14,
            'stoch_d': 3,
            'atr_period': 14
        }

    # If no specific indicators are selected, calculate all
    if selected_indicators is None:
        selected_indicators = [
            'ema', 'bollinger', 'sma', 'rsi', 'macd', 'stochastic', 'atr', 'roc', 'obv'
        ]

    # Create a loading placeholder
    loading_placeholder = st.empty()
    loading_placeholder.info("Calculating technical indicators...")

    # Calculate indicators based on selection
    if 'ema' in selected_indicators:
        # Calculate EMA with the selected span
        df[f"EMA_{params['ema_span']}"] = df.Close.ewm(span=params['ema_span'], adjust=False).mean()

    if 'bollinger' in selected_indicators:
        # Calculate Bollinger Bands
        df['SMA_20'] = df.Close.rolling(window=params['bb_period']).mean()
        df['STD_20'] = df.Close.rolling(window=params['bb_period']).std()
        df['Upper_Band'] = df['SMA_20'] + (df['STD_20'] * params['bb_std'])
        df['Lower_Band'] = df['SMA_20'] - (df['STD_20'] * params['bb_std'])

    if 'sma' in selected_indicators:
        # Calculate 200-day SMA for trend identification
        df['SMA_200'] = df.Close.rolling(window=params['sma_period']).mean()

    if 'rsi' in selected_indicators:
        # Calculate RSI - optimized calculation
        delta = df.Close.diff()
        gain = delta.where(delta > 0, 0)
        loss = -delta.where(delta < 0, 0)
        avg_gain = gain.rolling(window=params['rsi_period']).mean()
        avg_loss = loss.rolling(window=params['rsi_period']).mean()
        rs = avg_gain / avg_loss
        df['RSI'] = 100 - (100 / (1 + rs))

    if 'macd' in selected_indicators:
        # Calculate MACD - vectorized calculation
        ema_fast = df.Close.ewm(span=params['macd_fast'], adjust=False).mean()
        ema_slow = df.Close.ewm(span=params['macd_slow'], adjust=False).mean()
        df['MACD'] = ema_fast - ema_slow
        df['MACD_Signal'] = df['MACD'].ewm(span=params['macd_signal'], adjust=False).mean()
        df['MACD_Hist'] = df['MACD'] - df['MACD_Signal']

    if 'stochastic' in selected_indicators:
        # Calculate Stochastic Oscillator - vectorized calculation
        low_min = df.Low.rolling(window=params['stoch_k']).min()
        high_max = df.High.rolling(window=params['stoch_k']).max()
        df['Stoch_K'] = 100 * ((df.Close - low_min) / (high_max - low_min))
        df['Stoch_D'] = df['Stoch_K'].rolling(window=params['stoch_d']).mean()

    if 'atr' in selected_indicators:
        # Calculate Average True Range (ATR) - optimized calculation
        high_low = df.High - df.Low
        high_close = (df.High - df.Close.shift()).abs()
        low_close = (df.Low - df.Close.shift()).abs()
        true_range = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
        df['ATR'] = true_range.rolling(window=params['atr_period']).mean()

    if 'roc' in selected_indicators:
        # Calculate Rate of Change (ROC)
        df['ROC'] = df.Close.pct_change(periods=10) * 100

    if 'obv' in selected_indicators:
        # Calculate On-Balance Volume (OBV) - optimized calculation
        df['OBV'] = (np.sign(df.Close.diff()) * df.Volume).fillna(0).cumsum()

    # Clear loading message
    loading_placeholder.empty()

    return df

# Function to train AI on historical data
def train_ai_on_historical_data(df, symbol, timeframe_val):
    """
    Train the AI agent on historical data to find optimal entry and exit parameters.
    Stores the results in session state for use in live trading.
    """
    # Create a progress bar
    progress_bar = st.progress(0)
    status_text = st.empty()

    # Define parameter ranges to test
    entry_thresholds = [0.005, 0.01, 0.015, 0.02, 0.025]
    stop_loss_distances = [0.02, 0.03, 0.04, 0.05, 0.06]
    take_profit_levels = [0.03, 0.05, 0.07, 0.1, 0.15]
    confidence_thresholds = [0.5, 0.6, 0.7, 0.8]

    # Technical indicator parameters to optimize
    rsi_periods = [9, 14, 21]
    macd_fast_periods = [8, 12, 16]
    macd_slow_periods = [21, 26, 30]
    stoch_k_periods = [9, 14, 21]

    # Initialize best parameters and performance
    best_params = None
    best_score = -float('inf')

    # Calculate total combinations for progress tracking
    total_combinations = (
        len(entry_thresholds) *
        len(stop_loss_distances) *
        len(take_profit_levels) *
        len(rsi_periods) *
        len(macd_fast_periods)
    )

    # We'll use a subset of combinations to keep training time reasonable
    # Select one parameter from each category to optimize
    selected_rsi_period = rsi_periods[1]  # Default to middle value
    selected_macd_fast = macd_fast_periods[1]  # Default to middle value
    selected_macd_slow = macd_slow_periods[1]  # Default to middle value
    selected_stoch_k = stoch_k_periods[1]  # Default to middle value

    # Reduced total for progress tracking
    reduced_total = len(entry_thresholds) * len(stop_loss_distances) * len(take_profit_levels)
    current_combination = 0

    # Store results for all combinations
    results = []

    # First phase: Find best entry/exit parameters
    st.markdown("### Phase 1: Optimizing Entry and Exit Parameters")

    # Grid search through parameter combinations
    for entry_threshold in entry_thresholds:
        for stop_loss_distance in stop_loss_distances:
            for take_profit_level in take_profit_levels:
                # Update progress
                current_combination += 1
                progress = current_combination / reduced_total
                progress_bar.progress(progress)
                status_text.text(f"Testing combination {current_combination}/{reduced_total}: Entry={entry_threshold*100:.1f}%, Stop-Loss={stop_loss_distance*100:.1f}%")

                # Create trailing stop configuration
                trailing_config = TrailingStopConfig(
                    initial_distance=stop_loss_distance,
                    adaptive_adjustment=True,
                    volatility_factor=1.5,
                    min_distance=stop_loss_distance * 0.7,
                    max_distance=stop_loss_distance * 1.5
                )

                # Set up technical indicator parameters
                indicator_params = {
                    'ema_span': ema_span,
                    'rsi_period': selected_rsi_period,
                    'macd_fast': selected_macd_fast,
                    'macd_slow': selected_macd_slow,
                    'macd_signal': 9,
                    'bb_period': 20,
                    'bb_std': 2,
                    'sma_period': 200,
                    'stoch_k': selected_stoch_k,
                    'stoch_d': 3,
                    'atr_period': 14
                }

                # Calculate indicators
                temp_df = df.copy()
                temp_df = calculate_indicators(temp_df, indicator_params)

                # Backtest the strategy with these parameters
                buy_points, sell_points, trade_results = ai_trading_strategy(
                    temp_df,
                    entry_threshold,
                    stop_loss_distance,
                    f"EMA_{ema_span}",
                    use_agent=True,
                    trailing_config=trailing_config,
                    take_profit_level=take_profit_level
                )

                # Calculate performance metrics
                if trade_results:
                    trades_df = pd.DataFrame(trade_results)
                    total_trades = len(trades_df)
                    winning_trades = len(trades_df[trades_df['profit_pct'] > 0])
                    win_rate = winning_trades / total_trades if total_trades > 0 else 0
                    avg_profit = trades_df['profit_pct'].mean() if not trades_df.empty else 0
                    cumulative_return = ((1 + trades_df['profit_pct']).prod() - 1) if not trades_df.empty else 0

                    # Calculate profit factor
                    total_profit = trades_df[trades_df['profit_pct'] > 0]['profit_pct'].sum() if winning_trades > 0 else 0
                    total_loss = abs(trades_df[trades_df['profit_pct'] <= 0]['profit_pct'].sum()) if (total_trades - winning_trades) > 0 else 0
                    profit_factor = total_profit / total_loss if total_loss > 0 else (float('inf') if total_profit > 0 else 0)

                    # Calculate score based on multiple metrics
                    score = (
                        win_rate * 0.3 +
                        min(profit_factor, 3) / 3 * 0.3 +
                        min(cumulative_return * 5, 1) * 0.3 +
                        min(total_trades / 20, 1) * 0.1
                    )

                    # Store result
                    results.append({
                        'entry_threshold': entry_threshold,
                        'stop_loss_distance': stop_loss_distance,
                        'take_profit_level': take_profit_level,
                        'win_rate': win_rate,
                        'profit_factor': profit_factor,
                        'cumulative_return': cumulative_return,
                        'total_trades': total_trades,
                        'score': score
                    })

                    # Update best parameters if this is better
                    if score > best_score and total_trades >= 5:
                        best_score = score
                        best_params = {
                            'entry_threshold': entry_threshold,
                            'stop_loss_distance': stop_loss_distance,
                            'take_profit_level': take_profit_level,
                            'confidence_threshold': 0.6,  # Default confidence threshold
                            'win_rate': win_rate,
                            'profit_factor': profit_factor,
                            'cumulative_return': cumulative_return,
                            'total_trades': total_trades,
                            'score': score
                        }

    # Clear progress indicators
    progress_bar.empty()
    status_text.empty()

    # If we found good parameters in phase 1, proceed to phase 2
    if best_params:
        # Store the best parameters from phase 1
        best_entry_threshold = best_params['entry_threshold']
        best_stop_loss = best_params['stop_loss_distance']
        best_take_profit = best_params['take_profit_level']

        # Phase 2: Optimize technical indicators with the best entry/exit parameters
        st.markdown("### Phase 2: Optimizing Technical Indicators")

        # Reset progress tracking
        current_combination = 0
        indicator_combinations = len(rsi_periods) * len(macd_fast_periods) * len(stoch_k_periods)

        # Store indicator optimization results
        indicator_results = []

        # Grid search through indicator parameters
        for rsi_period in rsi_periods:
            for macd_fast in macd_fast_periods:
                for stoch_k in stoch_k_periods:
                    # Update progress
                    current_combination += 1
                    progress = current_combination / indicator_combinations
                    progress_bar.progress(progress)
                    status_text.text(f"Testing indicators {current_combination}/{indicator_combinations}: RSI={rsi_period}, MACD={macd_fast}")

                    # Use best MACD slow that's greater than fast
                    valid_macd_slow = [s for s in macd_slow_periods if s > macd_fast]
                    if not valid_macd_slow:
                        continue
                    macd_slow = valid_macd_slow[0]

                    # Create trailing stop configuration with best parameters
                    trailing_config = TrailingStopConfig(
                        initial_distance=best_stop_loss,
                        adaptive_adjustment=True,
                        volatility_factor=1.5,
                        min_distance=best_stop_loss * 0.7,
                        max_distance=best_stop_loss * 1.5
                    )

                    # Set up technical indicator parameters
                    indicator_params = {
                        'ema_span': ema_span,
                        'rsi_period': rsi_period,
                        'macd_fast': macd_fast,
                        'macd_slow': macd_slow,
                        'macd_signal': 9,
                        'bb_period': 20,
                        'bb_std': 2,
                        'sma_period': 200,
                        'stoch_k': stoch_k,
                        'stoch_d': 3,
                        'atr_period': 14
                    }

                    # Calculate indicators
                    temp_df = df.copy()
                    temp_df = calculate_indicators(temp_df, indicator_params)

                    # Backtest the strategy with these parameters
                    buy_points, sell_points, trade_results = ai_trading_strategy(
                        temp_df,
                        best_entry_threshold,
                        best_stop_loss,
                        f"EMA_{ema_span}",
                        use_agent=True,
                        trailing_config=trailing_config,
                        take_profit_level=best_take_profit
                    )

                    # Calculate performance metrics
                    if trade_results:
                        trades_df = pd.DataFrame(trade_results)
                        total_trades = len(trades_df)
                        winning_trades = len(trades_df[trades_df['profit_pct'] > 0])
                        win_rate = winning_trades / total_trades if total_trades > 0 else 0
                        avg_profit = trades_df['profit_pct'].mean() if not trades_df.empty else 0
                        cumulative_return = ((1 + trades_df['profit_pct']).prod() - 1) if not trades_df.empty else 0

                        # Calculate profit factor
                        total_profit = trades_df[trades_df['profit_pct'] > 0]['profit_pct'].sum() if winning_trades > 0 else 0
                        total_loss = abs(trades_df[trades_df['profit_pct'] <= 0]['profit_pct'].sum()) if (total_trades - winning_trades) > 0 else 0
                        profit_factor = total_profit / total_loss if total_loss > 0 else (float('inf') if total_profit > 0 else 0)

                        # Calculate score based on multiple metrics
                        score = (
                            win_rate * 0.3 +
                            min(profit_factor, 3) / 3 * 0.3 +
                            min(cumulative_return * 5, 1) * 0.3 +
                            min(total_trades / 20, 1) * 0.1
                        )

                        # Store result
                        indicator_results.append({
                            'rsi_period': rsi_period,
                            'macd_fast': macd_fast,
                            'macd_slow': macd_slow,
                            'stoch_k': stoch_k,
                            'win_rate': win_rate,
                            'profit_factor': profit_factor,
                            'cumulative_return': cumulative_return,
                            'total_trades': total_trades,
                            'score': score
                        })

                        # Update best parameters if this is better
                        if score > best_score and total_trades >= 5:
                            best_score = score
                            best_params.update({
                                'rsi_period': rsi_period,
                                'macd_fast': macd_fast,
                                'macd_slow': macd_slow,
                                'stoch_k': stoch_k,
                                'win_rate': win_rate,
                                'profit_factor': profit_factor,
                                'cumulative_return': cumulative_return,
                                'total_trades': total_trades,
                                'score': score
                            })

        # Fine-tune confidence threshold
        for confidence_threshold in confidence_thresholds:
            best_params['confidence_threshold'] = confidence_threshold

        # Store in session state
        st.session_state.ai_trained_parameters = best_params

        # Also store as recommended parameters for live trading
        st.session_state.ai_recommended_params = {
            'ema_span': ema_span,
            'entry_threshold': best_params['entry_threshold'],
            'trailing_stop_distance': best_params['stop_loss_distance'],
            'volatility_factor': 1.5,
            'confidence_threshold': best_params['confidence_threshold'],
            'take_profit_level': best_params['take_profit_level'],
            'rsi_period': best_params.get('rsi_period', 14),
            'macd_fast': best_params.get('macd_fast', 12),
            'macd_slow': best_params.get('macd_slow', 26),
            'stoch_k': best_params.get('stoch_k', 14)
        }

        # Display success message
        st.success(f"AI training complete! Found optimal parameters with score: {best_score:.2f}")

        # Create a DataFrame with all results for comparison
        results_df = pd.DataFrame(results)
        if not results_df.empty:
            results_df = results_df.sort_values('score', ascending=False).head(5)
            st.session_state.ai_training_results = results_df

        # Create a DataFrame with indicator results
        if indicator_results:
            indicator_df = pd.DataFrame(indicator_results)
            indicator_df = indicator_df.sort_values('score', ascending=False).head(5)
            st.session_state.ai_indicator_results = indicator_df
    else:
        st.warning("Could not find optimal parameters. Try with different data or parameter ranges.")

# Function to run live trading simulation
def run_live_trading():
    if not trading_active and not st.session_state.get('generate_new_signal', False):
        return

    # Get the latest data for the selected live trading timeframe
    current_time = datetime.now()
    lookback_period = {
        "1 Day": timedelta(days=30),
        "4 Hours": timedelta(days=14),
        "1 Hour": timedelta(days=7),
        "15 Minutes": timedelta(days=3),
        "5 Minutes": timedelta(days=1),
        "1 Minute": timedelta(hours=12)
    }

    # Use the live trading timeframe if available, otherwise fall back to the analysis timeframe
    selected_timeframe = live_timeframe if live_trading_enabled else timeframe
    selected_timeframe_val = live_timeframe_val if live_trading_enabled else timeframe_val

    live_start_date = current_time - lookback_period.get(selected_timeframe, timedelta(days=7))
    live_df = getdata(symbol, selected_timeframe_val, live_start_date)

    # Display the timeframe being used for live trading
    if live_trading_enabled and trading_active:
        st.sidebar.success(f"Trading on {selected_timeframe} timeframe")

    if not live_df.empty:
        # Calculate price, returns and EMA
        live_df["price"] = live_df.Close
        live_df["ret"] = live_df.Close.pct_change()

        # Check if we should use AI-trained parameters from Strategy Visualization
        if 'ai_trained_parameters' in st.session_state:
            trained_params = st.session_state.ai_trained_parameters
            ema_span_live = ema_span  # Use the current EMA span
            entry_threshold_live = trained_params.get('entry_threshold')
            distance_live = trained_params.get('stop_loss_distance')
            volatility_factor_live = 1.5  # Default value
            confidence_threshold = trained_params.get('confidence_threshold', 0.6)
            take_profit_level = trained_params.get('take_profit_level')

            # Notify user that we're using trained parameters
            st.sidebar.success("Using AI-trained parameters from Strategy Visualization")

        # Otherwise, check if we should use AI-recommended parameters from auto-optimization
        elif auto_optimize and 'ai_recommended_params' in st.session_state:
            rec_params = st.session_state.ai_recommended_params
            ema_span_live = rec_params.get('ema_span', ema_span)
            entry_threshold_live = rec_params.get('entry_threshold', entry_threshold)
            distance_live = rec_params.get('trailing_stop_distance', distance)
            volatility_factor_live = rec_params.get('volatility_factor', volatility_factor)
            confidence_threshold = rec_params.get('confidence_threshold', 0.6)
            take_profit_level = rec_params.get('take_profit_level')

            # Notify user that we're using optimized parameters
            st.sidebar.success("Using AI-optimized parameters")
        else:
            # Use current parameters
            ema_span_live = ema_span
            entry_threshold_live = entry_threshold
            distance_live = distance
            volatility_factor_live = volatility_factor
            confidence_threshold = 0.6
            take_profit_level = None  # No take profit by default

        # Get technical indicator parameters
        indicator_params = {
            'ema_span': ema_span_live,
            'rsi_period': st.session_state.ai_recommended_params.get('rsi_period', 14) if 'ai_recommended_params' in st.session_state else 14,
            'macd_fast': st.session_state.ai_recommended_params.get('macd_fast', 12) if 'ai_recommended_params' in st.session_state else 12,
            'macd_slow': st.session_state.ai_recommended_params.get('macd_slow', 26) if 'ai_recommended_params' in st.session_state else 26,
            'macd_signal': 9,
            'bb_period': 20,
            'bb_std': 2,
            'sma_period': 200,
            'stoch_k': st.session_state.ai_recommended_params.get('stoch_k', 14) if 'ai_recommended_params' in st.session_state else 14,
            'stoch_d': 3,
            'atr_period': 14
        }

        # Calculate all technical indicators
        live_df = calculate_indicators(live_df, indicator_params)

        # Set EMA column name for reference
        ema_col = f"EMA_{ema_span_live}"

        # Create trailing stop configuration with current or optimized parameters
        trailing_config = TrailingStopConfig(
            initial_distance=distance_live,
            adaptive_adjustment=adaptive_adjustment,
            volatility_factor=volatility_factor_live,
            min_distance=min_distance,
            max_distance=max_distance
        )

        # Create strategy parameters
        strategy_params = StrategyParameters(
            ema_span=ema_span_live,
            entry_threshold=entry_threshold_live,
            trailing_stop=trailing_config,
            use_ai_optimization=use_ai_optimization
        )

        # Initialize trading agent and store in session state
        agent = TradingAgent(strategy_params)
        st.session_state.trading_agent = agent

        # Prepare market data for agent
        market_data = agent.prepare_market_data(live_df)

        # Get entry signal
        signal = agent.get_entry_signal(market_data)

        # Only consider signals with confidence above threshold
        if signal and signal.confidence < confidence_threshold:
            st.sidebar.warning(f"Signal rejected: {signal.confidence:.2f} confidence below threshold {confidence_threshold:.2f}")
            signal = None

        # Display signal in chat-like interface
        with chat_container:
            if signal:
                # Add signal to session state for chart
                signal_data = {
                    "time": current_time,
                    "price": signal.entry_price,
                    "type": "BUY",
                    "confidence": signal.confidence,
                    "reason": signal.reason
                }
                st.session_state.trade_signals.append(signal_data)

                # Display signal in the signals column
                st.success(f"🤖 AI Agent ({current_time.strftime('%Y-%m-%d %H:%M:%S')})")
                st.markdown(f"**BUY Signal** for {symbol} at **{signal.entry_price:.2f}**")
                st.markdown(f"**Confidence:** {signal.confidence:.2f}")
                st.markdown(f"**Reason:** {signal.reason}")

                # Get optimized trailing stop
                optimal_stop = agent.optimize_trailing_stop(market_data)
                stop_price = signal.entry_price * (1 - optimal_stop)

                st.markdown(f"**Trailing Stop:** {optimal_stop:.2%} (Stop price: {stop_price:.2f})")
                st.markdown("---")
            else:
                # Occasionally show analysis even without a signal
                if np.random.random() < 0.3:  # 30% chance to show analysis
                    st.info(f"🤖 AI Agent ({current_time.strftime('%Y-%m-%d %H:%M:%S')})")
                    st.markdown(f"**Analysis for {symbol}**")
                    st.markdown("No entry signal at current price levels.")

                    # If AI optimization is enabled, show some analysis
                    if use_ai_optimization and agent.has_api_key:
                        analysis = agent._analyze_with_llm(market_data)
                        if analysis:
                            st.markdown(f"**Market sentiment:** {analysis['reason'][:100]}...")
                    st.markdown("---")

# Create a container for the loading spinner
loading_container = st.empty()

# Use a context manager for the spinner
with loading_container.container():
    with st.spinner('Initializing data processing...'):
        # Check if we have cached data for this configuration
        cache_key = f"{symbol}_{timeframe_val}_{start_date.strftime('%Y%m%d')}"

        if cache_key in st.session_state and not st.session_state.get('force_refresh', False):
            # Use cached data
            st.success("Using cached data")
            df = st.session_state[cache_key]
        else:
            # Show progress message
            progress_text = st.empty()
            progress_text.info("Step 1/4: Fetching historical data...")

            # Fetch historical data for the selected symbol and timeframe
            df = getdata(symbol, timeframe_val, start_date)

            if not df.empty:
                # Update progress
                progress_text.info("Step 2/4: Calculating basic metrics...")

                # Calculate price and returns
                df["price"] = df.Open.shift(-1)
                df["ret"] = df.Close.pct_change()

                # Determine if we should optimize parameters
                current_time = datetime.now()
                should_optimize = auto_optimize and st.session_state.parameter_optimizer.should_optimize(current_time)

                # Get optimized parameters if auto-optimization is enabled
                if should_optimize and len(df) > 100:
                    progress_text.info("Step 3/4: Optimizing strategy parameters...")
                    optimized_params = st.session_state.parameter_optimizer.optimize_parameters(df)
                    if optimized_params:
                        # Use optimized parameters
                        ema_span = optimized_params.ema_span
                        entry_threshold = optimized_params.entry_threshold
                        distance = optimized_params.trailing_stop.initial_distance
                        adaptive_adjustment = optimized_params.trailing_stop.adaptive_adjustment
                        volatility_factor = optimized_params.trailing_stop.volatility_factor
                        min_distance = optimized_params.trailing_stop.min_distance
                        max_distance = optimized_params.trailing_stop.max_distance

                        # Display optimization notification
                        st.sidebar.success(f"Parameters optimized at {current_time.strftime('%H:%M:%S')}")

                # Update progress
                progress_text.info("Step 4/4: Calculating technical indicators...")

                # Determine which indicators we need based on the selected tab
                selected_tab = st.session_state.get('selected_tab', 0)

                # For Strategy Visualization and Live Trades, we need all indicators
                if selected_tab in [0, 2]:
                    selected_indicators = None  # Calculate all
                # For Performance Metrics, we only need basic indicators
                elif selected_tab == 1:
                    selected_indicators = ['ema', 'bollinger', 'sma']
                # For AI Critique, we need all indicators
                else:
                    selected_indicators = None  # Calculate all

                # Set up indicator parameters
                indicator_params = {
                    'ema_span': ema_span,
                    'rsi_period': 14,
                    'macd_fast': 12,
                    'macd_slow': 26,
                    'macd_signal': 9,
                    'bb_period': 20,
                    'bb_std': 2,
                    'sma_period': 200,
                    'stoch_k': 14,
                    'stoch_d': 3,
                    'atr_period': 14
                }

                # Apply technical indicators with optimized calculation
                df = calculate_indicators(df, indicator_params, selected_indicators)

                # Drop NaN values
                df = df.dropna()

                # Cache the processed data
                st.session_state[cache_key] = df
                st.session_state['force_refresh'] = False

                # Clear progress message
                progress_text.empty()

                # Show success message
                st.success(f"Data processing complete: {len(df)} data points loaded")

# Clear the loading container
loading_container.empty()

# Check if we have valid data
if 'df' in locals() and not df.empty:
    # Create trailing stop configuration
    trailing_config = TrailingStopConfig(
        initial_distance=distance,
        adaptive_adjustment=adaptive_adjustment,
        volatility_factor=volatility_factor,
        min_distance=min_distance,
        max_distance=max_distance
    )

    # Get take profit level if available from AI training
    take_profit_level = None
    if 'ai_trained_parameters' in st.session_state:
        take_profit_level = st.session_state.ai_trained_parameters.get('take_profit_level')

    # Execute AI-driven strategy
    ema_col = f"EMA_{ema_span}"
    buy_points, sell_points, trade_results = ai_trading_strategy(
        df,
        entry_threshold,
        distance,
        ema_col,
        use_agent=use_ai_optimization,
        trailing_config=trailing_config,
        take_profit_level=take_profit_level
    )

    # Analyze trade performance with critique agent
    if trade_results:
        trades_df = pd.DataFrame(trade_results)
        performance = st.session_state.critique_agent.analyze_performance(trades_df)
        feedback = st.session_state.critique_agent.get_performance_feedback(performance)
    else:
        trades_df = pd.DataFrame(columns=['entry_date', 'exit_date', 'entry_price', 'exit_price', 'profit_pct', 'profit_abs'])
        performance = None
        feedback = {"status": "neutral", "message": "Not enough trades to evaluate performance.", "suggestions": []}

    # Strategy Visualization Tab
    with tabs[0]:
        # Prepare indicators for chart display
        indicators = {}
        if f'EMA_{ema_span}' in df.columns:
            indicators[f'EMA_{ema_span}'] = df[f'EMA_{ema_span}']

        # Add optional indicators based on user selection
        show_bb = st.sidebar.checkbox('Show Bollinger Bands', value=False, key="main_bb_checkbox")
        show_sma = st.sidebar.checkbox('Show 200 SMA', value=False, key="main_sma_checkbox")

        if show_bb and 'Upper_Band' in df.columns:
            indicators['Upper_Band'] = df['Upper_Band']
            indicators['Lower_Band'] = df['Lower_Band']
        if show_sma and 'SMA_200' in df.columns:
            indicators['SMA_200'] = df['SMA_200']

        # Create chart based on selected type
        if chart_type == "Candlestick Chart" and PLOTLY_AVAILABLE:
            # Create interactive candlestick chart
            fig = create_candlestick_chart(
                df=df,
                buy_points=buy_points,
                sell_points=sell_points,
                indicators=indicators,
                title=f'{symbol} - {timeframe_val} Trading Strategy Visualization',
                show_volume=show_volume,
                theme=chart_theme
            )
            if fig:
                st.plotly_chart(fig, use_container_width=True)
        else:
            # Fallback to matplotlib line chart
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10), gridspec_kw={'height_ratios': [3, 1]}, sharex=True)

            # Price and indicators plot
            ax1.plot(df.index, df['Close'], label='Price', color='blue')
            ax1.plot(df.index, df[ema_col], label=f'{ema_span} EMA', linestyle='--', color='orange')

            # Add bands if user wants them
            if show_bb and 'Upper_Band' in df.columns:
                ax1.plot(df.index, df['Upper_Band'], 'r--', alpha=0.3)
                ax1.plot(df.index, df['Lower_Band'], 'r--', alpha=0.3)
                ax1.fill_between(df.index, df['Upper_Band'], df['Lower_Band'], color='gray', alpha=0.1)

            # Add SMA if user wants it
            if show_sma and 'SMA_200' in df.columns:
                ax1.plot(df.index, df['SMA_200'], 'g--', alpha=0.5, label='200 SMA')

            # Format x-axis dates
            ax1.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
            plt.xticks(rotation=45)

            # Add buy/sell markers
            buy_label_used = False
            sell_label_used = False

            for time, price in buy_points:
                if not buy_label_used:
                    ax1.scatter(time, price, color='green', marker='^', s=100, label='Buy Signal')
                    buy_label_used = True
                else:
                    ax1.scatter(time, price, color='green', marker='^', s=100)

            for time, price in sell_points:
                if not sell_label_used:
                    ax1.scatter(time, price, color='red', marker='v', s=100, label='Sell Signal')
                    sell_label_used = True
                else:
                    ax1.scatter(time, price, color='red', marker='v', s=100)

            ax1.set_title(f'{symbol} Price Chart with Trading Strategy ({timeframe})')
            ax1.set_ylabel('Price (USDT)')
            ax1.grid(True)
            ax1.legend(loc='upper left')

            # Volume subplot
            ax2.bar(df.index, df['Volume'], color='blue', alpha=0.3, label='Volume')
            ax2.set_ylabel('Volume')
            ax2.grid(True, alpha=0.3)

            # Adjust layout and display
            plt.tight_layout()
            st.pyplot(fig)

        # Display recent signals
        st.subheader("Recent Signals")
        recent_signals = []

        # Get the 5 most recent buy signals
        for i, (time, price) in enumerate(reversed(buy_points)):
            if i >= 5: break
            recent_signals.append({"Time": time, "Type": "Buy", "Price": price})

        # Get the 5 most recent sell signals
        for i, (time, price) in enumerate(reversed(sell_points)):
            if i >= 5: break
            recent_signals.append({"Time": time, "Type": "Sell", "Price": price})

        # Sort by time (most recent first)
        if recent_signals:
            recent_signals_df = pd.DataFrame(recent_signals)
            recent_signals_df = recent_signals_df.sort_values("Time", ascending=False).head(5)
            st.dataframe(recent_signals_df, use_container_width=True)
        else:
            st.info("No signals generated with current parameters.")

        # Add AI Training section
        st.markdown("---")
        st.subheader("AI Training")

        col1, col2 = st.columns([3, 1])
        with col1:
            st.markdown("""
            Train the AI agent to find optimal entry points and stop-loss levels based on the current chart data.
            The trained parameters will be used for live trading signals.
            """)
        with col2:
            if st.button("Train AI", key="train_ai_btn"):
                with st.spinner("Training AI on historical data..."):
                    # We'll implement the training function next
                    st.session_state.ai_training_in_progress = True

        # Show training status if in progress or completed
        if st.session_state.get('ai_training_in_progress', False):
            train_ai_on_historical_data(df, symbol, timeframe_val)
            st.session_state.ai_training_in_progress = False

        if st.session_state.get('ai_trained_parameters', None):
            st.success("✅ AI training completed!")
            params = st.session_state.ai_trained_parameters

            # Display the trained parameters
            st.markdown("### Optimized Trading Parameters")
            params_df = pd.DataFrame({
                'Parameter': ['Entry Threshold', 'Stop-Loss Distance', 'Take Profit Level', 'Confidence Threshold'],
                'Value': [
                    f"{params['entry_threshold']*100:.2f}%",
                    f"{params['stop_loss_distance']*100:.2f}%",
                    f"{params['take_profit_level']*100:.2f}%",
                    f"{params['confidence_threshold']:.2f}"
                ]
            })
            st.table(params_df)

            # Display the optimized technical indicators if available
            if any(k in params for k in ['rsi_period', 'macd_fast', 'macd_slow', 'stoch_k']):
                st.markdown("### Optimized Technical Indicators")
                indicator_df = pd.DataFrame({
                    'Indicator': ['RSI Period', 'MACD Fast', 'MACD Slow', 'Stochastic K'],
                    'Value': [
                        params.get('rsi_period', 14),
                        params.get('macd_fast', 12),
                        params.get('macd_slow', 26),
                        params.get('stoch_k', 14)
                    ]
                })
                st.table(indicator_df)

                # Display performance metrics
                st.markdown("### Performance Metrics")
                metrics_df = pd.DataFrame({
                    'Metric': ['Win Rate', 'Profit Factor', 'Cumulative Return', 'Total Trades', 'Score'],
                    'Value': [
                        f"{params['win_rate']*100:.2f}%",
                        f"{params['profit_factor']:.2f}" if params['profit_factor'] != float('inf') else "∞",
                        f"{params['cumulative_return']*100:.2f}%",
                        f"{params['total_trades']}",
                        f"{params['score']:.2f}"
                    ]
                })
                st.table(metrics_df)

                # Display top parameter combinations if available
                if 'ai_training_results' in st.session_state:
                    st.markdown("### Top Parameter Combinations")
                    results_df = st.session_state.ai_training_results.copy()

                    # Format the results for display
                    results_df['entry_threshold'] = results_df['entry_threshold'].apply(lambda x: f"{x*100:.2f}%")
                    results_df['stop_loss_distance'] = results_df['stop_loss_distance'].apply(lambda x: f"{x*100:.2f}%")
                    results_df['take_profit_level'] = results_df['take_profit_level'].apply(lambda x: f"{x*100:.2f}%")
                    results_df['win_rate'] = results_df['win_rate'].apply(lambda x: f"{x*100:.2f}%")
                    results_df['profit_factor'] = results_df['profit_factor'].apply(lambda x: f"{x:.2f}" if x != float('inf') else "∞")
                    results_df['cumulative_return'] = results_df['cumulative_return'].apply(lambda x: f"{x*100:.2f}%")
                    results_df['score'] = results_df['score'].apply(lambda x: f"{x:.2f}")

                    # Rename columns for display
                    results_df.rename(columns={
                        'entry_threshold': 'Entry Threshold',
                        'stop_loss_distance': 'Stop-Loss',
                        'take_profit_level': 'Take Profit',
                        'win_rate': 'Win Rate',
                        'profit_factor': 'Profit Factor',
                        'cumulative_return': 'Return',
                        'total_trades': 'Trades',
                        'score': 'Score'
                    }, inplace=True)

                    st.dataframe(results_df, use_container_width=True)

                # Add a button to apply these parameters to live trading
                if st.button("Use for Live Trading", key="use_for_live_trading_btn"):
                    st.session_state.ai_recommended_params = {
                        'ema_span': ema_span,
                        'entry_threshold': params['entry_threshold'],
                        'trailing_stop_distance': params['stop_loss_distance'],
                        'volatility_factor': 1.5,
                        'confidence_threshold': params['confidence_threshold'],
                        'take_profit_level': params['take_profit_level']
                    }
                    st.success("Parameters applied to live trading! Switch to the Live Trades tab to see them in action.")

            # Option to show raw data
            st.markdown("---")
            if st.checkbox("Show Raw Data", value=False, key="show_raw_data_checkbox"):
                st.dataframe(df, use_container_width=True)

        # Performance Metrics Tab
        with tabs[1]:
            if not trades_df.empty:
                # Calculate performance metrics
                total_trades = len(trades_df)
                winning_trades = len(trades_df[trades_df['profit_pct'] > 0])
                losing_trades = len(trades_df[trades_df['profit_pct'] <= 0])

                if total_trades > 0:
                    win_rate = (winning_trades / total_trades) * 100
                    avg_profit = trades_df['profit_pct'].mean() * 100
                    avg_win = trades_df[trades_df['profit_pct'] > 0]['profit_pct'].mean() * 100 if winning_trades > 0 else 0
                    avg_loss = trades_df[trades_df['profit_pct'] <= 0]['profit_pct'].mean() * 100 if losing_trades > 0 else 0
                    max_profit = trades_df['profit_pct'].max() * 100
                    max_loss = trades_df['profit_pct'].min() * 100

                    # Calculate cumulative returns
                    cumulative_return = ((1 + trades_df['profit_pct']).prod() - 1) * 100

                    # Calculate Sharpe ratio if we have enough trades
                    sharpe_ratio = None
                    if len(trades_df) > 5:
                        returns = trades_df['profit_pct']
                        sharpe_ratio = (returns.mean() / returns.std()) * np.sqrt(252)  # Annualized

                    # Calculate profit factor
                    total_profit = trades_df[trades_df['profit_pct'] > 0]['profit_pct'].sum() if winning_trades > 0 else 0
                    total_loss = abs(trades_df[trades_df['profit_pct'] <= 0]['profit_pct'].sum()) if losing_trades > 0 else 0
                    profit_factor = total_profit / total_loss if total_loss > 0 else float('inf') if total_profit > 0 else 0

                    # Display metrics in columns
                    col1, col2, col3 = st.columns(3)
                    with col1:
                        st.metric("Total Trades", total_trades)
                        st.metric("Win Rate", f"{win_rate:.2f}%")
                        st.metric("Cumulative Return", f"{cumulative_return:.2f}%")
                        if sharpe_ratio:
                            st.metric("Sharpe Ratio", f"{sharpe_ratio:.2f}")

                    with col2:
                        st.metric("Winning Trades", winning_trades)
                        st.metric("Average Profit", f"{avg_profit:.2f}%")
                        st.metric("Max Profit", f"{max_profit:.2f}%")
                        st.metric("Profit Factor", f"{profit_factor:.2f}" if profit_factor != float('inf') else "∞")

                    with col3:
                        st.metric("Losing Trades", losing_trades)
                        st.metric("Average Loss", f"{avg_loss:.2f}%" if losing_trades > 0 else "0.00%")
                        st.metric("Max Loss", f"{max_loss:.2f}%" if losing_trades > 0 else "0.00%")

                        # Display current strategy parameters
                        st.metric("Current EMA", ema_span)

                    # Plot equity curve
                    st.subheader("Equity Curve")

                    # Calculate cumulative returns for each trade
                    trades_df['cumulative_return'] = (1 + trades_df['profit_pct']).cumprod() - 1

                    # Plot equity curve
                    fig, ax = plt.subplots(figsize=(10, 6))
                    ax.plot(trades_df['exit_date'], trades_df['cumulative_return'] * 100)
                    ax.set_title('Strategy Equity Curve')
                    ax.set_xlabel('Date')
                    ax.set_ylabel('Cumulative Return (%)')
                    ax.grid(True)
                    st.pyplot(fig)

                    # Display trade history
                    st.subheader("Trade History")

                    # Format the trades dataframe for display
                    display_df = trades_df.copy()
                    display_df['profit_pct'] = display_df['profit_pct'] * 100
                    display_df['profit_pct'] = display_df['profit_pct'].map('{:.2f}%'.format)
                    display_df.rename(columns={
                        'entry_date': 'Entry Date',
                        'exit_date': 'Exit Date',
                        'entry_price': 'Entry Price',
                        'exit_price': 'Exit Price',
                        'profit_pct': 'Profit/Loss',
                        'profit_abs': 'Absolute Profit/Loss'
                    }, inplace=True)

                    st.dataframe(display_df, use_container_width=True)
                else:
                    st.info("No trades were executed with the current parameters.")

        # AI Critique Tab
        with tabs[3]:
            st.subheader("AI Trading Performance Analysis")

            if performance and performance.total_trades > 0:
                # Display AI feedback with appropriate styling
                if feedback["status"] == "excellent":
                    st.success(feedback["message"])
                elif feedback["status"] == "good":
                    st.info(feedback["message"])
                elif feedback["status"] == "poor":
                    st.error(feedback["message"])
                else:
                    st.warning(feedback["message"])

                # Display suggestions
                if feedback["suggestions"]:
                    st.subheader("Improvement Suggestions")
                    for i, suggestion in enumerate(feedback["suggestions"]):
                        st.markdown(f"**{i+1}.** {suggestion}")

                # Display current and recommended parameters
                st.subheader("Strategy Parameters")

                # Check if we should optimize parameters
                if auto_optimize and st.session_state.critique_agent.should_optimize_parameters():
                    with st.spinner("Analyzing optimal parameters..."):
                        # Prepare market data
                        market_data = None
                        if 'trading_agent' in st.session_state:
                            market_data = st.session_state.trading_agent.prepare_market_data(df)
                        else:
                            # Create a temporary agent to prepare market data
                            temp_agent = TradingAgent(StrategyParameters(
                                ema_span=ema_span,
                                entry_threshold=entry_threshold,
                                trailing_stop=trailing_config,
                                use_ai_optimization=use_ai_optimization
                            ))
                            market_data = temp_agent.prepare_market_data(df)

                        # Get current parameters
                        current_params = StrategyParameters(
                            ema_span=ema_span,
                            entry_threshold=entry_threshold,
                            trailing_stop=trailing_config,
                            use_ai_optimization=use_ai_optimization
                        )

                        # Get optimized parameters
                        recommendation = st.session_state.critique_agent.optimize_parameters(
                            market_data, current_params, performance)

                        # Display recommendation
                        st.subheader("AI Parameter Recommendations")
                        st.markdown(f"**Reason:** {recommendation.reason}")

                        # Display parameter comparison
                        param_comparison = pd.DataFrame({
                            'Parameter': ['EMA Span', 'Entry Threshold', 'Trailing Stop', 'Volatility Factor'],
                            'Current': [
                                str(ema_span),
                                f"{entry_threshold*100:.2f}%",
                                f"{distance*100:.2f}%",
                                str(volatility_factor)
                            ],
                            'Recommended': [
                                str(recommendation.ema_span),
                                f"{recommendation.entry_threshold*100:.2f}%",
                                f"{recommendation.trailing_stop_distance*100:.2f}%",
                                str(recommendation.volatility_factor)
                            ]
                        })
                        st.table(param_comparison)

                        # Add button to apply recommendations
                        if st.button("Apply AI Recommendations", key="apply_ai_recommendations_btn"):
                            # Store in session state to be used in next run
                            st.session_state.ai_recommended_params = {
                                'ema_span': recommendation.ema_span,
                                'entry_threshold': recommendation.entry_threshold,
                                'trailing_stop_distance': recommendation.trailing_stop_distance,
                                'volatility_factor': recommendation.volatility_factor,
                                'confidence_threshold': recommendation.confidence_threshold
                            }
                            st.success("AI recommendations applied! The changes will take effect on the next update.")
                else:
                    # Display current parameters
                    current_params = {
                        'EMA Span': ema_span,
                        'Entry Threshold': f"{entry_threshold*100:.2f}%",
                        'Trailing Stop': f"{distance*100:.2f}%",
                        'Volatility Factor': volatility_factor,
                        'Min Distance': f"{min_distance*100:.2f}%",
                        'Max Distance': f"{max_distance*100:.2f}%"
                    }

                    # Convert to DataFrame for display
                    params_df = pd.DataFrame(list(current_params.items()), columns=['Parameter', 'Value'])
                    st.table(params_df)
            else:
                st.info("Not enough trading data to provide AI analysis. Execute more trades to enable this feature.")

                # Show sample analysis for demonstration
                if st.button("Show Sample Analysis", key="show_sample_analysis_btn"):
                    st.success("Strategy is performing well with a positive trend.")
                    st.markdown("**Sample Recommendations:**")
                    st.markdown("1. Consider decreasing EMA span to 35 for more responsive signals in current market conditions")
                    st.markdown("2. Increase trailing stop distance to 6.5% to capture larger trends")
                    st.markdown("3. Adjust volatility factor to 1.2 based on recent market behavior")

                    # Sample parameter comparison
                    sample_comparison = pd.DataFrame({
                        'Parameter': ['EMA Span', 'Entry Threshold', 'Trailing Stop', 'Volatility Factor'],
                        'Current': [
                            str(ema_span),
                            f"{entry_threshold*100:.2f}%",
                            f"{distance*100:.2f}%",
                            str(volatility_factor)
                        ],
                        'Recommended': [
                            "35",
                            "0.80%",
                            "6.50%",
                            "1.2"
                        ]
                    })
                    st.table(sample_comparison)
                else:
                    st.info("No trades were executed with the current parameters.")

        # Live Trades Tab
        with tabs[2]:
            st.subheader("Live Trading Signals")

            if not live_trading_enabled:
                st.info("Enable Live Trading in the sidebar to start receiving real-time trading signals.")

                # Add some guidance about timeframes
                st.markdown("### Trading Timeframe Guide")
                st.markdown("""
                When you enable Live Trading, you can select a specific timeframe for the AI to trade on:
                - **1 Day**: Long-term trend trading (less signals, higher quality)
                - **4 Hours**: Swing trading (balanced approach)
                - **1 Hour**: Intraday trading (more signals)
                - **15 Minutes**: Short-term trading (frequent signals)
                - **5 Minutes**: Scalping (very frequent signals, higher risk)
                - **1 Minute**: Ultra-short term (experimental, high risk)

                Different timeframes work better for different market conditions. The AI will adapt its strategy based on the selected timeframe.
                """)
            else:
                # Add timeframe selector for live trading
                st.sidebar.markdown("### Live Trading Timeframe")
                live_timeframe_options = ["1 Minute", "5 Minutes", "15 Minutes", "1 Hour", "4 Hours", "1 Day"]
                selected_live_timeframe = st.sidebar.selectbox(
                    "Select timeframe for live trading:",
                    options=live_timeframe_options,
                    index=live_timeframe_options.index(live_timeframe),
                    key="live_timeframe_selector"
                )

                # Update the timeframe if changed
                if selected_live_timeframe != live_timeframe:
                    live_timeframe = selected_live_timeframe
                    # Map to API timeframe format
                    timeframe_map = {
                        "1 Minute": "1m",
                        "5 Minutes": "5m",
                        "15 Minutes": "15m",
                        "1 Hour": "1h",
                        "4 Hours": "4h",
                        "1 Day": "1d"
                    }
                    live_timeframe_val = timeframe_map[live_timeframe]
                    # Force refresh data
                    st.session_state.force_refresh = True
                    st.experimental_rerun()

                # Create two columns for signals and chart
                signal_col, chart_col = st.columns([1, 2])

                # Container for the chat-like interface in the left column
                with signal_col:
                    st.subheader("AI Trading Signals")

                    # Add timeframe indicator
                    st.info(f"🕒 Trading on {live_timeframe} timeframe")

                    chat_container = st.container()

                    # Add a button to generate new signals manually
                    col1, col2 = st.columns([3, 1])
                    with col1:
                        if st.button("Generate New Signal", key="generate_signal_btn"):
                            st.session_state.generate_new_signal = True
                        else:
                            st.session_state.generate_new_signal = False

                    # Display auto-refresh option
                    with col2:
                        auto_refresh = st.checkbox("Auto-refresh", value=False, key="auto_refresh_checkbox")
                        if auto_refresh:
                            st.session_state.last_refresh = time.time()
                            # Auto-refresh every 10 seconds
                            if time.time() - st.session_state.get('last_refresh', 0) > 10:
                                st.session_state.last_refresh = time.time()
                                st.session_state.generate_new_signal = True

                # Container for the trading chart in the right column
                with chart_col:
                    st.subheader("Live Trading Chart")
                    chart_container = st.container()

                # Initialize session state for storing trade signals if not exists
                if 'trade_signals' not in st.session_state:
                    st.session_state.trade_signals = []

                # Initialize session state for storing sell signals if not exists
                if 'sell_signals' not in st.session_state:
                    st.session_state.sell_signals = []

                # Function to simulate live trading with more frequent signals
                def run_live_trading():
                    # Always run if generate_new_signal is True, regardless of trading_active
                    if not trading_active and not st.session_state.get('generate_new_signal', False):
                        return

                    # Get the latest data for the selected timeframe
                    current_time = datetime.now()
                    lookback_period = {
                        "1 Day": timedelta(days=30),
                        "4 Hours": timedelta(days=14),
                        "1 Hour": timedelta(days=7),
                        "15 Minutes": timedelta(days=3),
                        "5 Minutes": timedelta(days=1),
                        "1 Minute": timedelta(hours=12)
                    }

                    live_start_date = current_time - lookback_period.get(timeframe, timedelta(days=30))
                    live_df = getdata(symbol, timeframe_val, live_start_date)

                    if not live_df.empty:
                        # Calculate price, returns and EMA
                        live_df["price"] = live_df.Close
                        live_df["ret"] = live_df.Close.pct_change()

                        # Check if we should use AI-recommended parameters from previous analysis
                        if auto_optimize and 'ai_recommended_params' in st.session_state:
                            rec_params = st.session_state.ai_recommended_params
                            ema_span_live = rec_params.get('ema_span', ema_span)
                            entry_threshold_live = rec_params.get('entry_threshold', entry_threshold)
                            distance_live = rec_params.get('trailing_stop_distance', distance)
                            volatility_factor_live = rec_params.get('volatility_factor', volatility_factor)
                            confidence_threshold = rec_params.get('confidence_threshold', 0.6)

                            # Notify user that we're using optimized parameters
                            st.sidebar.success("Using AI-optimized parameters")
                        else:
                            # Use current parameters
                            ema_span_live = ema_span
                            entry_threshold_live = entry_threshold
                            distance_live = distance
                            volatility_factor_live = volatility_factor
                            confidence_threshold = 0.6

                        # Calculate EMA with current or optimized span
                        ema_col = f"EMA_{ema_span_live}"
                        live_df[ema_col] = live_df.Close.ewm(span=ema_span_live, adjust=False).mean()

                        # Create trailing stop configuration with current or optimized parameters
                        trailing_config = TrailingStopConfig(
                            initial_distance=distance_live,
                            adaptive_adjustment=adaptive_adjustment,
                            volatility_factor=volatility_factor_live,
                            min_distance=min_distance,
                            max_distance=max_distance
                        )

                        # Create strategy parameters
                        strategy_params = StrategyParameters(
                            ema_span=ema_span_live,
                            entry_threshold=entry_threshold_live,
                            trailing_stop=trailing_config,
                            use_ai_optimization=use_ai_optimization
                        )

                        # Initialize trading agent and store in session state
                        agent = TradingAgent(strategy_params)
                        st.session_state.trading_agent = agent

                        # Prepare market data for agent
                        market_data = agent.prepare_market_data(live_df)

                        # Get entry signal
                        signal = agent.get_entry_signal(market_data)

                        # Store take profit level in the signal for display
                        if signal and take_profit_level:
                            signal.take_profit_level = take_profit_level

                        # Only consider signals with confidence above threshold
                        if signal and signal.confidence < confidence_threshold:
                            st.sidebar.warning(f"Signal rejected: {signal.confidence:.2f} confidence below threshold {confidence_threshold:.2f}")
                            signal = None

                        # Display signal in chat-like interface
                        with chat_container:
                            if signal:
                                # Add signal to session state for chart
                                signal_data = {
                                    "time": current_time,
                                    "price": signal.entry_price,
                                    "type": "BUY",
                                    "confidence": signal.confidence,
                                    "reason": signal.reason
                                }
                                st.session_state.trade_signals.append(signal_data)

                                # Display signal in the signals column
                                st.success(f"🤖 AI Agent ({current_time.strftime('%Y-%m-%d %H:%M:%S')})")
                                st.markdown(f"**BUY Signal** for {symbol} at **{signal.entry_price:.2f}**")
                                st.markdown(f"**Confidence:** {signal.confidence:.2f}")
                                st.markdown(f"**Reason:** {signal.reason}")

                                # Get optimized trailing stop
                                optimal_stop = agent.optimize_trailing_stop(market_data)
                                stop_price = signal.entry_price * (1 - optimal_stop)

                                # Display stop loss information
                                st.markdown(f"**Trailing Stop:** {optimal_stop:.2%} (Stop price: {stop_price:.2f})")

                                # Display take profit information if available
                                if hasattr(signal, 'take_profit_level') and signal.take_profit_level:
                                    take_profit_price = signal.entry_price * (1 + signal.take_profit_level)
                                    st.markdown(f"**Take Profit:** {signal.take_profit_level:.2%} (Target price: {take_profit_price:.2f})")

                                # Display parameter source
                                if 'ai_trained_parameters' in st.session_state:
                                    st.info("Using AI-trained parameters from Strategy Visualization")

                                st.markdown("---")
                            else:
                                # Occasionally show analysis even without a signal
                                if np.random.random() < 0.3:  # 30% chance to show analysis
                                    st.info(f"🤖 AI Agent ({current_time.strftime('%Y-%m-%d %H:%M:%S')})")
                                    st.markdown(f"**Analysis for {symbol}**")
                                    st.markdown("No entry signal at current price levels.")

                                    # If AI optimization is enabled, show some analysis
                                    if use_ai_optimization and agent.has_api_key:
                                        analysis = agent._analyze_with_llm(market_data)
                                        if analysis:
                                            st.markdown(f"**Market sentiment:** {analysis['reason'][:100]}...")
                                    st.markdown("---")

                        # Display trading chart in the chart column
                        with chart_container:
                            # Create a figure for the trading chart with enhanced visualization
                            fig, (ax, ax2) = plt.subplots(2, 1, figsize=(10, 6), gridspec_kw={'height_ratios': [3, 1]}, sharex=True)

                            # Plot price and EMA on main chart
                            ax.plot(live_df.index[-100:], live_df['Close'][-100:], label='Price', color='blue')
                            ax.plot(live_df.index[-100:], live_df[ema_col][-100:], label=f'{ema_span} EMA', linestyle='--', color='orange')

                            # Add RSI indicator if available
                            if 'RSI' in live_df.columns:
                                # Add RSI overbought/oversold lines
                                ax.axhline(y=live_df['Close'].iloc[-100] * 1.05, color='r', linestyle='--', alpha=0.3, label='Overbought')
                                ax.axhline(y=live_df['Close'].iloc[-100] * 0.95, color='g', linestyle='--', alpha=0.3, label='Oversold')

                            # Add current price marker
                            current_time = datetime.now()
                            current_price = live_df['Close'].iloc[-1]
                            ax.scatter(current_time, current_price, color='yellow', marker='o', s=100, zorder=5, label='Current')

                            # Add buy/sell signals from session state with enhanced visualization
                            buy_label_used = False
                            sell_label_used = False

                            for signal in st.session_state.trade_signals:
                                # Only plot signals that fall within the chart timeframe
                                if signal['time'] >= live_df.index[-100].to_pydatetime():
                                    if signal['type'] == 'BUY':
                                        label = 'AI Buy Signal' if not buy_label_used else ""
                                        ax.scatter(signal['time'], signal['price'], color='green', marker='^', s=100, label=label)
                                        buy_label_used = True

                                        # Add annotation with confidence score
                                        ax.annotate(f"{signal['price']:.0f} ({signal['confidence']:.2f})",
                                                   (signal['time'], signal['price']),
                                                   xytext=(0, 10),
                                                   textcoords='offset points',
                                                   fontsize=8,
                                                   bbox=dict(boxstyle='round,pad=0.3', fc='green', alpha=0.3))
                                    elif signal['type'] == 'SELL':
                                        label = 'AI Sell Signal' if not sell_label_used else ""
                                        ax.scatter(signal['time'], signal['price'], color='red', marker='v', s=100, label=label)
                                        sell_label_used = True

                                        # Add annotation with confidence score
                                        ax.annotate(f"{signal['price']:.0f} ({signal['confidence']:.2f})",
                                                   (signal['time'], signal['price']),
                                                   xytext=(0, -15),
                                                   textcoords='offset points',
                                                   fontsize=8,
                                                   bbox=dict(boxstyle='round,pad=0.3', fc='red', alpha=0.3))

                            # Add Bollinger Bands if enabled - use a unique key with timestamp
                            bb_key = f"live_bb_checkbox_{int(time.time() * 1000)}"
                            if st.sidebar.checkbox('Show Bollinger Bands in Live Chart', value=False, key=bb_key):
                                # Calculate Bollinger Bands
                                live_df['Upper_Band'] = live_df[ema_col] + (live_df['Close'].rolling(window=20).std() * 2)
                                live_df['Lower_Band'] = live_df[ema_col] - (live_df['Close'].rolling(window=20).std() * 2)

                                ax.plot(live_df.index[-100:], live_df['Upper_Band'][-100:], 'r--', alpha=0.3, label='Upper Band')
                                ax.plot(live_df.index[-100:], live_df['Lower_Band'][-100:], 'r--', alpha=0.3, label='Lower Band')
                                ax.fill_between(live_df.index[-100:], live_df['Upper_Band'][-100:], live_df['Lower_Band'][-100:], color='gray', alpha=0.1)

                            # Format the main chart
                            ax.set_title(f'{symbol} Live Trading Chart ({live_timeframe})')
                            ax.set_ylabel('Price (USDT)')
                            ax.grid(True)
                            ax.legend(loc='upper left')

                            # Add volume subplot
                            ax2.bar(live_df.index[-100:], live_df['Volume'][-100:], color='blue', alpha=0.3, label='Volume')
                            ax2.set_ylabel('Volume')
                            ax2.grid(True, alpha=0.3)

                            # Add a horizontal line for average volume
                            avg_volume = live_df['Volume'][-100:].mean()
                            ax2.axhline(y=avg_volume, color='r', linestyle='-', alpha=0.3, label='Avg Volume')
                            ax2.legend(loc='upper left')

                            # Format x-axis dates
                            ax2.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
                            plt.xticks(rotation=45)

                            # Adjust layout
                            plt.tight_layout()

                            # Display the chart with a key to force refresh
                            chart_key = f"live_chart_{int(time.time())}"
                            st.pyplot(fig, key=chart_key)

                            # Add a live trading status indicator
                            if trading_active:
                                st.success("🔴 LIVE: Trading bot is actively monitoring the market")

                                # Simulate price movement for demo purposes
                                if 'last_price' not in st.session_state:
                                    st.session_state.last_price = live_df['Close'].iloc[-1]
                                    st.session_state.price_direction = 1 if np.random.random() > 0.5 else -1

                                # Update price with small random movement
                                price_change = np.random.normal(0, 0.001) * st.session_state.last_price
                                if np.random.random() < 0.05:  # 5% chance to change direction
                                    st.session_state.price_direction *= -1
                                price_change *= st.session_state.price_direction

                                current_price = st.session_state.last_price + price_change
                                st.session_state.last_price = current_price

                                # Display current price with up/down indicator
                                price_delta = price_change
                                price_col1, price_col2 = st.columns([1, 1])
                                with price_col1:
                                    st.metric("Current Price", f"${current_price:.2f}", f"{price_delta:.2f}")
                                with price_col2:
                                    if st.session_state.trade_signals:
                                        last_signal = st.session_state.trade_signals[-1]
                                        time_since = (datetime.now() - last_signal['time']).total_seconds()
                                        if time_since < 60:
                                            time_str = f"{int(time_since)} seconds ago"
                                        elif time_since < 3600:
                                            time_str = f"{int(time_since/60)} minutes ago"
                                        else:
                                            time_str = f"{int(time_since/3600)} hours ago"
                                        st.info(f"Last signal: {last_signal['type']} ({time_str})")

                            # Display trade signals summary with enhanced visualization
                            if st.session_state.trade_signals:
                                st.subheader("AI Trade Signals Summary")
                                signals_df = pd.DataFrame(st.session_state.trade_signals)
                                signals_df['time'] = signals_df['time'].apply(lambda x: x.strftime('%Y-%m-%d %H:%M:%S'))
                                signals_df.rename(columns={
                                    'time': 'Time',
                                    'price': 'Price',
                                    'type': 'Signal Type',
                                    'confidence': 'Confidence'
                                }, inplace=True)

                                # Add color coding to the dataframe
                                def highlight_signals(val):
                                    if val == 'BUY':
                                        return 'background-color: rgba(0, 255, 0, 0.2)'
                                    elif val == 'SELL':
                                        return 'background-color: rgba(255, 0, 0, 0.2)'
                                    return ''

                                styled_df = signals_df[['Time', 'Signal Type', 'Price', 'Confidence']].style.applymap(
                                    highlight_signals, subset=['Signal Type']
                                )
                                st.dataframe(styled_df, use_container_width=True)

                                # Check if we should optimize after a trade
                                if optimization_frequency == "After each trade" and auto_optimize:
                                    if len(st.session_state.trade_signals) > 0 and len(df) > 100:
                                        with st.spinner('Optimizing strategy parameters after trade...'):
                                            optimized_params = st.session_state.parameter_optimizer.optimize_parameters(df)
                                            if optimized_params:
                                                st.success("Strategy parameters optimized after trade")
                                                # Store optimized parameters for next run
                                                st.session_state.ai_recommended_params = {
                                                    'ema_span': optimized_params.ema_span,
                                                    'entry_threshold': optimized_params.entry_threshold,
                                                    'trailing_stop_distance': optimized_params.trailing_stop.initial_distance,
                                                    'volatility_factor': optimized_params.trailing_stop.volatility_factor,
                                                    'confidence_threshold': 0.6
                                                }

                                # Add AI critique of recent trades
                                if st.session_state.trade_signals and len(st.session_state.trade_signals) > 1:
                                    st.subheader("AI Trade Analysis")

                                    # Create a container for AI feedback
                                    ai_feedback = st.container()
                                    with ai_feedback:
                                        # Generate mock trade performance data from signals
                                        if 'sell_signals' not in st.session_state:
                                            st.session_state.sell_signals = []

                                        # Create mock trade results for demonstration
                                        mock_trades = []
                                        for buy_signal in st.session_state.trade_signals:
                                            # For demonstration, create some mock results
                                            entry_price = buy_signal['price']
                                            # Simulate some random exit prices
                                            exit_price = entry_price * (1 + np.random.normal(0.02, 0.05))
                                            profit_pct = (exit_price - entry_price) / entry_price

                                            mock_trades.append({
                                                'entry_date': buy_signal['time'],
                                                'exit_date': buy_signal['time'] + timedelta(hours=random.randint(1, 24)),
                                                'entry_price': entry_price,
                                                'exit_price': exit_price,
                                                'profit_pct': profit_pct,
                                                'profit_abs': exit_price - entry_price
                                            })

                                        if mock_trades:
                                            # Create DataFrame from mock trades
                                            mock_df = pd.DataFrame(mock_trades)

                                            # Analyze with critique agent
                                            performance = st.session_state.critique_agent.analyze_performance(mock_df)
                                            feedback = st.session_state.critique_agent.get_performance_feedback(performance)

                                            # Display feedback with appropriate styling
                                            if feedback["status"] == "excellent":
                                                st.success("🌟 " + feedback["message"])
                                            elif feedback["status"] == "good":
                                                st.info("✅ " + feedback["message"])
                                            elif feedback["status"] == "poor":
                                                st.error("⚠️ " + feedback["message"])
                                            else:
                                                st.warning("ℹ️ " + feedback["message"])

                                            # Display suggestions
                                            if feedback["suggestions"]:
                                                for suggestion in feedback["suggestions"]:
                                                    st.markdown(f"💡 **Suggestion:** {suggestion}")
                                        else:
                                            st.info("Complete some trades to receive AI analysis")

                # Live trading is handled at the app level, not recursively called here

                # Initialize a trading performance monitor
                if 'performance_monitor' not in st.session_state:
                    st.session_state.performance_monitor = {
                        'trades': [],
                        'daily_pnl': 0.0,
                        'win_streak': 0,
                        'loss_streak': 0,
                        'last_check': datetime.now()
                    }

                # Add a trading performance monitor section
                st.subheader("Trading Performance Monitor")
                monitor_container = st.container()

                with monitor_container:
                      # Display current trading stats
                      if st.session_state.performance_monitor['trades']:
                          trades = st.session_state.performance_monitor['trades']
                          wins = sum(1 for t in trades if t['profit_pct'] > 0)
                          losses = sum(1 for t in trades if t['profit_pct'] <= 0)

                          # Create metrics display
                          col1, col2, col3 = st.columns(3)
                          with col1:
                              st.metric("Total Trades", len(trades))
                              st.metric("Daily P&L", f"{st.session_state.performance_monitor['daily_pnl']:.2f}%")
                          with col2:
                              st.metric("Win Rate", f"{(wins/len(trades)*100):.1f}%" if len(trades) > 0 else "0.0%")
                              st.metric("Win Streak", st.session_state.performance_monitor['win_streak'])
                          with col3:
                              st.metric("Avg Profit", f"{np.mean([t['profit_pct'] for t in trades])*100:.2f}%" if trades else "0.0%")
                              st.metric("Loss Streak", st.session_state.performance_monitor['loss_streak'])

                          # Check if we need to stop trading due to max daily loss
                          if trading_active and st.session_state.performance_monitor['daily_pnl'] < -max_daily_loss:
                              st.error(f"⛔ Trading automatically stopped: Daily loss limit of {max_daily_loss}% reached")
                              trading_active = False
                      else:
                          st.info("No trades executed yet today. Performance metrics will appear here.")
                if trading_active:
                    st.success("🟢 Live trading is active")
                    # Don't call run_live_trading() here to avoid recursion
                else:
                    st.error("🔴 Live trading is stopped")

                # Add a refresh button to manually trigger updates
                if st.button("Refresh Signals", key="refresh_signals_btn"):
                    st.experimental_rerun()  # Rerun the app instead of calling run_live_trading() directly

                # Add explanation
                st.markdown("""
                ### How Live Trading Works:
                - The AI agent analyzes market data in real-time based on your selected timeframe
                - When conditions are favorable, it generates buy signals with confidence scores
                - The agent calculates optimal trailing stop levels based on current market volatility
                - Click 'Refresh Signals' to manually check for new signals

                *Note: This is a simulation for educational purposes. No actual trades are executed.*
                """)

        # About Tab
        with tabs[3]:
            st.subheader("About This App")
            st.markdown("""
            This application visualizes a trading strategy based on Exponential Moving Average (EMA) and trailing stop loss.

            ### Strategy Logic:
            - **Entry Condition**: Price returns exceed the threshold AND price is above the EMA
            - **Exit Condition**: Price falls below trailing stop OR price falls below the EMA

            ### Parameters:
            - **EMA Span**: The period used for the Exponential Moving Average calculation
            - **Trailing Distance**: The percentage distance for the trailing stop loss
            - **Entry Threshold**: The minimum return required to enter a trade

            ### Disclaimer:
            This tool is for educational purposes only. Past performance is not indicative of future results.
            Always conduct your own research before making investment decisions.
            """)
else:
    st.error("Failed to fetch data. Please check your internet connection or try different parameters.")
