import pandas as pd
import numpy as np
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Optional, Tuple, Any, Union
from pydantic import BaseModel, Field
import json
import os
import re
from langchain.agents import Tool, AgentExecutor, LLMSingleActionAgent
from langchain.prompts import String<PERSON>romptTemplate
from langchain.chains import <PERSON><PERSON><PERSON>n
from langchain_community.chat_models import ChatOpenAI
from langchain.tools import BaseTool
from langchain.schema import AgentAction, AgentFinish
from langchain.agents import AgentOutputParser
from langchain.memory import ConversationBufferMemory
from langchain.callbacks.manager import CallbackManagerForToolRun
from langchain.callbacks.base import BaseCallbackHandler
from trading_agent import MarketData, TrailingStopConfig, StrategyParameters, TradingAgent
from parameter_optimizer import ParameterOptimizer, OptimizationResult
from critique_agent import TradePerformance, ParameterRecommendation

class StrategyTrainerAgent:
    """Agent that trains and optimizes trading strategies to maximize win rate and profitability."""
    
    def __init__(self, openai_api_key=None):
        self.model = "gpt-4o-mini"
        self.has_api_key = openai_api_key is not None
        self.openai_api_key = openai_api_key
        self.parameter_optimizer = ParameterOptimizer()
        self.optimization_history = []
        self.best_strategy = None
        self.training_iterations = 0
        self.max_iterations = 10  # Maximum number of training iterations
        
        # Initialize LangChain components if API key is available
        if self.has_api_key:
            self.llm = ChatOpenAI(
                model_name=self.model,
                temperature=0.2,
                openai_api_key=self.openai_api_key
            )
            self.setup_langchain_agent()
    
    def setup_langchain_agent(self):
        """Set up the LangChain agent with tools and prompt template"""
        # Define tools for the agent
        tools = [
            Tool(
                name="BacktestStrategy",
                func=self._backtest_strategy,
                description="Backtests a trading strategy with given parameters on historical data"
            ),
            Tool(
                name="OptimizeParameters",
                func=self._optimize_parameters,
                description="Optimizes strategy parameters using grid search or genetic algorithms"
            ),
            Tool(
                name="AnalyzePerformance",
                func=self._analyze_performance,
                description="Analyzes the performance of a trading strategy"
            ),
            Tool(
                name="SuggestImprovements",
                func=self._suggest_improvements,
                description="Suggests improvements to a trading strategy based on performance analysis"
            )
        ]
        
        # Define prompt template
        prompt = StrategyTrainerPromptTemplate(
            template="""You are a Strategy Trainer Agent that optimizes trading strategies for cryptocurrency markets.
            Your goal is to find the best possible trading strategy that maximizes win rate and profitability.
            
            Current strategy parameters:
            {strategy_params}
            
            Recent performance metrics:
            {performance_metrics}
            
            Market conditions:
            {market_conditions}
            
            Your task is to analyze the current strategy, suggest improvements, and optimize parameters.
            
            Available tools:
            {tools}
            
            Use the tools to improve the strategy. Think step by step about what information you need and how to improve the strategy.
            
            {agent_scratchpad}
            """,
            input_variables=["strategy_params", "performance_metrics", "market_conditions", "tools", "agent_scratchpad"]
        )
        
        # Set up memory
        memory = ConversationBufferMemory(memory_key="chat_history")
        
        # Create LLM chain
        llm_chain = LLMChain(llm=self.llm, prompt=prompt)
        
        # Create agent
        agent = LLMSingleActionAgent(
            llm_chain=llm_chain,
            output_parser=StrategyTrainerOutputParser(),
            stop=["\nObservation:"],
            allowed_tools=[tool.name for tool in tools]
        )
        
        # Create agent executor
        self.agent_executor = AgentExecutor.from_agent_and_tools(
            agent=agent,
            tools=tools,
            verbose=True,
            memory=memory
        )
    
    def train_strategy(self, historical_data: pd.DataFrame, initial_params: StrategyParameters = None) -> StrategyParameters:
        """Train and optimize a trading strategy using historical data"""
        if not self.has_api_key:
            # Fallback to traditional optimization if no API key
            return self.parameter_optimizer.optimize_parameters(historical_data)
        
        # Initialize parameters if not provided
        if initial_params is None:
            trailing_config = TrailingStopConfig(
                initial_distance=0.05,
                adaptive_adjustment=True,
                volatility_factor=1.0,
                min_distance=0.01,
                max_distance=0.1
            )
            initial_params = StrategyParameters(
                ema_span=50,
                entry_threshold=0.01,
                trailing_stop=trailing_config,
                use_ai_optimization=True
            )
        
        # Prepare market data
        agent = TradingAgent(initial_params)
        market_data = agent.prepare_market_data(historical_data)
        
        # Run initial backtest to get baseline performance
        backtest_results = self._backtest_strategy_internal(historical_data, initial_params)
        performance = self._calculate_performance(backtest_results)
        
        # Store initial results
        self.optimization_history.append({
            "iteration": 0,
            "parameters": initial_params.model_dump(),
            "performance": performance.model_dump()
        })
        
        # Set best strategy to initial strategy
        self.best_strategy = initial_params
        best_performance = performance
        
        # Run training iterations
        for i in range(1, self.max_iterations + 1):
            try:
                # Get market conditions
                market_conditions = agent.analyze_market_conditions(market_data)
                
                # Run agent to get improved parameters
                result = self.agent_executor.run(
                    strategy_params=json.dumps(initial_params.model_dump()),
                    performance_metrics=json.dumps(performance.model_dump()),
                    market_conditions=json.dumps(market_conditions)
                )
                
                # Parse result to get new parameters
                new_params = self._parse_agent_result(result, initial_params)
                
                # Run backtest with new parameters
                backtest_results = self._backtest_strategy_internal(historical_data, new_params)
                new_performance = self._calculate_performance(backtest_results)
                
                # Store results
                self.optimization_history.append({
                    "iteration": i,
                    "parameters": new_params.model_dump(),
                    "performance": new_performance.model_dump()
                })
                
                # Update best strategy if performance improved
                if self._is_performance_better(new_performance, best_performance):
                    self.best_strategy = new_params
                    best_performance = new_performance
                
                # Update parameters for next iteration
                initial_params = new_params
                performance = new_performance
                
                self.training_iterations = i
                
            except Exception as e:
                print(f"Error in training iteration {i}: {str(e)}")
                break
        
        return self.best_strategy
    
    def _backtest_strategy(self, params_str: str) -> str:
        """Tool function to backtest a strategy with given parameters"""
        try:
            # Parse parameters
            params_dict = json.loads(params_str)
            trailing_config = TrailingStopConfig(
                initial_distance=params_dict.get("trailing_stop", {}).get("initial_distance", 0.05),
                adaptive_adjustment=params_dict.get("trailing_stop", {}).get("adaptive_adjustment", True),
                volatility_factor=params_dict.get("trailing_stop", {}).get("volatility_factor", 1.0),
                min_distance=params_dict.get("trailing_stop", {}).get("min_distance", 0.01),
                max_distance=params_dict.get("trailing_stop", {}).get("max_distance", 0.1)
            )
            strategy_params = StrategyParameters(
                ema_span=params_dict.get("ema_span", 50),
                entry_threshold=params_dict.get("entry_threshold", 0.01),
                trailing_stop=trailing_config,
                use_ai_optimization=params_dict.get("use_ai_optimization", True)
            )
            
            # Get historical data from session state
            # Note: In a real implementation, this would need to be passed in or stored
            # For now, we'll return a mock result
            return json.dumps({
                "total_trades": 50,
                "winning_trades": 35,
                "losing_trades": 15,
                "win_rate": 0.7,
                "avg_profit": 0.025,
                "avg_loss": -0.015,
                "cumulative_return": 0.45
            })
            
        except Exception as e:
            return f"Error in backtesting: {str(e)}"
    
    def _backtest_strategy_internal(self, historical_data: pd.DataFrame, params: StrategyParameters) -> Dict[str, Any]:
        """Internal function to backtest a strategy with given parameters"""
        # Prepare data
        df = historical_data.copy()
        
        # Calculate EMA
        ema_col = f"EMA_{params.ema_span}"
        if ema_col not in df.columns:
            df[ema_col] = df['Close'].ewm(span=params.ema_span, adjust=False).mean()
        
        # Calculate returns
        if 'ret' not in df.columns:
            df['ret'] = df['Close'].pct_change()
        
        # Initialize variables for backtesting
        buy_points = []
        sell_points = []
        trade_results = []
        in_position = False
        entry_price = 0
        trailing_stop = 0
        
        # Run backtest
        for i, (index, row) in enumerate(df.iterrows()):
            # Skip first few rows to allow indicators to stabilize
            if i < 20:
                continue
            
            # Entry logic
            if not in_position and row.ret > params.entry_threshold:
                if row.Close > row[ema_col]:  # Basic condition: price above EMA
                    entry_price = row.Close
                    in_position = True
                    trailing_stop = entry_price * (1 - params.trailing_stop.initial_distance)
                    buy_points.append((index, entry_price))
            
            # Exit logic
            elif in_position:
                # Update trailing stop if price moves in favorable direction
                if params.trailing_stop.adaptive_adjustment:
                    # Calculate adaptive trailing stop based on recent volatility
                    recent_data = df.iloc[:i+1]
                    recent_volatility = recent_data['Close'].pct_change().rolling(window=min(20, len(recent_data))).std().iloc[-1]
                    if pd.isna(recent_volatility):
                        recent_volatility = 0.02  # Default value if not enough data
                    stop_distance = min(
                        params.trailing_stop.max_distance,
                        max(
                            params.trailing_stop.min_distance,
                            params.trailing_stop.initial_distance * (1 + recent_volatility * params.trailing_stop.volatility_factor)
                        )
                    )
                    potential_new_stop = row.Close * (1 - stop_distance)
                else:
                    potential_new_stop = row.Close * (1 - params.trailing_stop.initial_distance)
                
                if potential_new_stop > trailing_stop:
                    trailing_stop = potential_new_stop
                
                # Check exit conditions
                if row.Close <= trailing_stop or row.Close < row[ema_col]:  # Exit condition
                    exit_price = row.Close
                    sell_points.append((index, exit_price))
                    
                    # Calculate trade result
                    profit_pct = (exit_price - entry_price) / entry_price
                    trade_results.append({
                        'entry_date': buy_points[-1][0],
                        'exit_date': index,
                        'entry_price': entry_price,
                        'exit_price': exit_price,
                        'profit_pct': profit_pct,
                        'profit_abs': exit_price - entry_price,
                        'exit_reason': 'stop_loss' if row.Close <= trailing_stop else 'trend_change'
                    })
                    
                    in_position = False
                    trailing_stop = 0  # Reset trailing stop for next trade
        
        # Handle open position at the end of the period
        if in_position and len(buy_points) > len(sell_points):
            last_price = df.iloc[-1].Close
            profit_pct = (last_price - entry_price) / entry_price
            trade_results.append({
                'entry_date': buy_points[-1][0],
                'exit_date': df.index[-1],
                'entry_price': entry_price,
                'exit_price': last_price,
                'profit_pct': profit_pct,
                'profit_abs': last_price - entry_price,
                'status': 'open',
                'exit_reason': 'end_of_period'
            })
        
        return {
            'buy_points': buy_points,
            'sell_points': sell_points,
            'trade_results': trade_results
        }
    
    def _calculate_performance(self, backtest_results: Dict[str, Any]) -> TradePerformance:
        """Calculate performance metrics from backtest results"""
        trade_results = backtest_results['trade_results']
        
        if not trade_results:
            return TradePerformance(
                total_trades=0,
                winning_trades=0,
                losing_trades=0,
                win_rate=0.0,
                avg_profit=0.0,
                avg_loss=0.0,
                max_profit=0.0,
                max_loss=0.0,
                cumulative_return=0.0
            )
        
        # Convert to DataFrame for easier analysis
        trades_df = pd.DataFrame(trade_results)
        
        # Calculate performance metrics
        total_trades = len(trades_df)
        winning_trades = len(trades_df[trades_df['profit_pct'] > 0])
        losing_trades = len(trades_df[trades_df['profit_pct'] <= 0])
        
        win_rate = (winning_trades / total_trades) if total_trades > 0 else 0.0
        avg_profit = trades_df[trades_df['profit_pct'] > 0]['profit_pct'].mean() if winning_trades > 0 else 0.0
        avg_loss = trades_df[trades_df['profit_pct'] <= 0]['profit_pct'].mean() if losing_trades > 0 else 0.0
        max_profit = trades_df['profit_pct'].max() if not trades_df.empty else 0.0
        max_loss = trades_df['profit_pct'].min() if not trades_df.empty else 0.0
        
        # Calculate cumulative return
        cumulative_return = ((1 + trades_df['profit_pct']).prod() - 1) if not trades_df.empty else 0.0
        
        # Calculate Sharpe ratio (if we have enough data)
        sharpe_ratio = None
        if len(trades_df) > 5:
            returns = trades_df['profit_pct']
            sharpe_ratio = (returns.mean() / returns.std()) * np.sqrt(252)  # Annualized
        
        # Calculate maximum drawdown
        drawdown = None
        if not trades_df.empty:
            # Calculate equity curve
            trades_df['cumulative_return'] = (1 + trades_df['profit_pct']).cumprod()
            # Calculate drawdown
            trades_df['peak'] = trades_df['cumulative_return'].cummax()
            trades_df['drawdown'] = (trades_df['cumulative_return'] - trades_df['peak']) / trades_df['peak']
            drawdown = trades_df['drawdown'].min()
        
        return TradePerformance(
            total_trades=total_trades,
            winning_trades=winning_trades,
            losing_trades=losing_trades,
            win_rate=win_rate,
            avg_profit=avg_profit,
            avg_loss=avg_loss,
            max_profit=max_profit,
            max_loss=max_loss,
            cumulative_return=cumulative_return,
            sharpe_ratio=sharpe_ratio,
            drawdown=drawdown
        )
    
    def _optimize_parameters(self, constraints_str: str) -> str:
        """Tool function to optimize parameters with given constraints"""
        try:
            # Parse constraints
            constraints = json.loads(constraints_str)
            
            # Call parameter optimizer
            # Note: In a real implementation, this would use historical data
            # For now, we'll return a mock result
            return json.dumps({
                "ema_span": 75,
                "entry_threshold": 0.015,
                "trailing_stop": {
                    "initial_distance": 0.04,
                    "adaptive_adjustment": True,
                    "volatility_factor": 1.5,
                    "min_distance": 0.02,
                    "max_distance": 0.12
                },
                "use_ai_optimization": True
            })
            
        except Exception as e:
            return f"Error in parameter optimization: {str(e)}"
    
    def _analyze_performance(self, results_str: str) -> str:
        """Tool function to analyze performance of a strategy"""
        try:
            # Parse results
            results = json.loads(results_str)
            
            # Calculate additional metrics
            profit_factor = abs(results.get("avg_profit", 0) * results.get("winning_trades", 0)) / \
                          abs(results.get("avg_loss", 0) * results.get("losing_trades", 0)) \
                          if results.get("losing_trades", 0) > 0 and results.get("avg_loss", 0) != 0 else float('inf')
            
            expectancy = (results.get("win_rate", 0) * results.get("avg_profit", 0)) + \
                       ((1 - results.get("win_rate", 0)) * results.get("avg_loss", 0))
            
            analysis = {
                "profit_factor": profit_factor,
                "expectancy": expectancy,
                "risk_reward_ratio": abs(results.get("avg_profit", 0) / results.get("avg_loss", 0)) if results.get("avg_loss", 0) != 0 else float('inf'),
                "trades_per_day": results.get("total_trades", 0) / 30,  # Assuming 30 days of data
                "assessment": self._assess_performance(results)
            }
            
            return json.dumps(analysis)
            
        except Exception as e:
            return f"Error in performance analysis: {str(e)}"
    
    def _assess_performance(self, results: Dict[str, Any]) -> str:
        """Assess the performance of a strategy"""
        win_rate = results.get("win_rate", 0)
        cumulative_return = results.get("cumulative_return", 0)
        
        if win_rate > 0.7 and cumulative_return > 0.3:
            return "excellent"
        elif win_rate > 0.6 and cumulative_return > 0.2:
            return "very_good"
        elif win_rate > 0.5 and cumulative_return > 0.1:
            return "good"
        elif win_rate > 0.4 and cumulative_return > 0:
            return "average"
        else:
            return "poor"
    
    def _suggest_improvements(self, analysis_str: str) -> str:
        """Tool function to suggest improvements based on performance analysis"""
        try:
            # Parse analysis
            analysis = json.loads(analysis_str)
            
            suggestions = []
            
            # Suggest improvements based on assessment
            assessment = analysis.get("assessment", "")
            
            if assessment == "poor":
                suggestions.append("Consider increasing the EMA span for more reliable trend identification")
                suggestions.append("Increase the entry threshold to filter out weak signals")
                suggestions.append("Adjust trailing stop to be more conservative")
            elif assessment == "average":
                suggestions.append("Fine-tune the entry threshold to improve signal quality")
                suggestions.append("Optimize trailing stop parameters based on recent volatility")
            elif assessment == "good":
                suggestions.append("Consider adding additional confirmation indicators")
                suggestions.append("Fine-tune volatility factor for trailing stop")
            elif assessment in ["very_good", "excellent"]:
                suggestions.append("Consider reducing the minimum trailing stop distance to lock in profits faster")
                suggestions.append("Experiment with more aggressive entry thresholds")
            
            # Suggest improvements based on specific metrics
            profit_factor = analysis.get("profit_factor", 0)
            if profit_factor < 1.5:
                suggestions.append("Focus on improving the profit factor by increasing average profit or reducing average loss")
            
            risk_reward_ratio = analysis.get("risk_reward_ratio", 0)
            if risk_reward_ratio < 1.5:
                suggestions.append("Improve risk-reward ratio by letting profits run longer or cutting losses quicker")
            
            return json.dumps({
                "suggestions": suggestions,
                "priority": "high" if assessment in ["poor", "average"] else "medium"
            })
            
        except Exception as e:
            return f"Error in suggesting improvements: {str(e)}"
    
    def _parse_agent_result(self, result: str, current_params: StrategyParameters) -> StrategyParameters:
        """Parse the result from the agent to get new parameters"""
        try:
            # Try to extract JSON from the result
            import re
            json_match = re.search(r'\{.*\}', result, re.DOTALL)
            if json_match:
                params_dict = json.loads(json_match.group(0))
            else:
                # If no JSON found, return current parameters
                return current_params
            
            # Create new parameters
            trailing_config = TrailingStopConfig(
                initial_distance=params_dict.get("trailing_stop", {}).get("initial_distance", current_params.trailing_stop.initial_distance),
                adaptive_adjustment=params_dict.get("trailing_stop", {}).get("adaptive_adjustment", current_params.trailing_stop.adaptive_adjustment),
                volatility_factor=params_dict.get("trailing_stop", {}).get("volatility_factor", current_params.trailing_stop.volatility_factor),
                min_distance=params_dict.get("trailing_stop", {}).get("min_distance", current_params.trailing_stop.min_distance),
                max_distance=params_dict.get("trailing_stop", {}).get("max_distance", current_params.trailing_stop.max_distance)
            )
            
            return StrategyParameters(
                ema_span=params_dict.get("ema_span", current_params.ema_span),
                entry_threshold=params_dict.get("entry_threshold", current_params.entry_threshold),
                trailing_stop=trailing_config,
                use_ai_optimization=params_dict.get("use_ai_optimization", current_params.use_ai_optimization)
            )
            
        except Exception as e:
            print(f"Error parsing agent result: {str(e)}")
            return current_params
    
    def _is_performance_better(self, new_perf: TradePerformance, current_perf: TradePerformance) -> bool:
        """Determine if new performance is better than current performance"""
        # Define weights for different metrics
        weights = {
            "win_rate": 0.3,
            "cumulative_return": 0.4,
            "sharpe_ratio": 0.2,
            "drawdown": 0.1
        }
        
        # Calculate weighted score for current performance
        current_score = (
            weights["win_rate"] * current_perf.win_rate +
            weights["cumulative_return"] * current_perf.cumulative_return +
            weights["sharpe_ratio"] * (current_perf.sharpe_ratio or 0) +
            weights["drawdown"] * (abs(current_perf.drawdown or 0) * -1)  # Negative weight for drawdown
        )
        
        # Calculate weighted score for new performance
        new_score = (
            weights["win_rate"] * new_perf.win_rate +
            weights["cumulative_return"] * new_perf.cumulative_return +
            weights["sharpe_ratio"] * (new_perf.sharpe_ratio or 0) +
            weights["drawdown"] * (abs(new_perf.drawdown or 0) * -1)  # Negative weight for drawdown
        )
        
        return new_score > current_score


class StrategyTrainerPromptTemplate(StringPromptTemplate):
    """Prompt template for the Strategy Trainer Agent"""
    
    template: str
    input_variables: List[str]
    
    def format(self, **kwargs) -> str:
        # Get the intermediate steps (AgentAction, Observation tuples)
        # Format them in a particular way
        intermediate_steps = kwargs.pop("intermediate_steps", [])
        thoughts = ""
        
        for action, observation in intermediate_steps:
            thoughts += f"\nAction: {action.tool}\nAction Input: {action.tool_input}\nObservation: {observation}\n"
        
        # Set the agent_scratchpad variable to that value
        kwargs["agent_scratchpad"] = thoughts
        
        # Create a tools variable from the list of tools provided
        kwargs["tools"] = "\n".join([f"{tool.name}: {tool.description}" for tool in kwargs["tools"]])
        
        return self.template.format(**kwargs)


class StrategyTrainerOutputParser(AgentOutputParser):
    """Output parser for the Strategy Trainer Agent"""
    
    def parse(self, llm_output: str) -> Union[AgentAction, AgentFinish]:
        # Check if the output indicates the agent is finished
        if "Final Answer:" in llm_output:
            return AgentFinish(
                return_values={"output": llm_output.split("Final Answer:")[-1].strip()},
                log=llm_output
            )
        
        # Parse out the action and action input
        regex = r"Action: (.*?)[\n]*Action Input: (.*)"
        match = re.search(regex, llm_output, re.DOTALL)
        
        if not match:
            raise ValueError(f"Could not parse LLM output: `{llm_output}`")
        
        action = match.group(1).strip()
        action_input = match.group(2).strip()
        
        return AgentAction(tool=action, tool_input=action_input, log=llm_output)